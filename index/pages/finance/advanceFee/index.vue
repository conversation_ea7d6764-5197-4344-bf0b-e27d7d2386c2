<template>
  <cd-page
    class="advance-fee"
    type="tab">
    <template v-if="tabs.length > 0">
      <cd-tabs
        :headers="tabs"
        :active="active"
        @change="$onTabChange">
        <cd-tab
          v-for="tab in tabs"
          :header="tab.tabname"
          :key="tab.code"></cd-tab>
      </cd-tabs>
      <template v-if="tabs[active].hasRule">
        <component
          v-bind:is="tabView"
          :accountTypeList="accountTypeList"></component>
      </template>
      <div
        v-else
        class="tac mt30 tip-text">
        {{ $lang(`暂无权限`) }}
      </div>
    </template>
    <div
      v-else
      class="tac mt30 tip-text">
      {{ $lang(`暂无权限`) }}
    </div>
  </cd-page>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import cdPage from 'project/index/components/Page';
import record from './record/index.vue';
import management from './management/index.vue';
import { eventBus } from 'build/plugs/utils';
export default {
  mixins: [eventBus],
  name: 'advanceFee',
  components: {
    cdPage,
    record,
    management
  },
  data() {
    return {
      active: 0,
      tabView: 'record',
      tabs: []
    };
  },
  computed: {
    accountTypeList() {
      let {
          organizationList
        } = Com.user.data,
        type = -1;
      let arr = [1, 2, 4, 3]; // 优先级
      for (const item of arr) {
        let obj = organizationList.find(v => v.type === item);
        if (obj) {
          type = item;
          break;
        }
      }
      /*
       运营商账号：全部可选   1
       品牌账号：品牌账户，门店账户，自定账户  2
       门店账号：门店账户 3
       组织架构账号(自定义组织): 自定义账户，门店账户  4
       */

      let list = [{
        name: i18n.tt("全部"),
        id: null
      }, {
        name: i18n.tt("运营商账户"),
        id: 1
      }, {
        name: i18n.tt("品牌账户"),
        id: 2
      }, {
        name: i18n.tt("自定义账户"),
        id: 4
      }, {
        name: i18n.tt("门店账户"),
        id: 3
      }];
      switch (type) {
        case 2:
          list.splice(1, 1);
          break;
        case 3:
          list.splice(1, 3);
          break;
        case 4:
          list.splice(1, 2);
          break;
      }
      return list;
    }
  },
  created() {
    let that = this;
    let arr = [{
      tabname: i18n.tt("交易记录"),
      component: 'record',
      code: '000800040001',
      hasRead: true,
      hasRule: true
    }, {
      tabname: i18n.tt("账户管理"),
      component: 'management',
      code: '000800040002',
      hasRead: true,
      hasRule: true
    }];
    arr.forEach(v => {
      if (Com.$confirmFunctionBtn(v.code).hasRead) {
        v.hasRule = Com.$confirmFunctionBtn(v.code).hasRule;
        this.tabs.push(v);
      }
    });
    if (this.tabs.length > 0) {
      this.$onTabChange(0);
    }
    this.$bus.$on('finance_advanceFee_index', data => {
      if (this.tabs[data]) {
        setTimeout(() => {
          that.$onTabChange(data);
        }, 100);
      }
    });
  },
  methods: {
    $onTabChange(index) {
      if (!Com.$confirmFunctionBtn(this.tabs[index].code).hasRule) {
        Com.showRuleTip();
        return;
      }
      if (this.tabs[index].tabname === i18n.tt("账户管理")) {
        this.$router.push({
          query: {}
        });
      }
      this.active = index;
      this.tabView = this.tabs[index].component;
    }
  }
}
</script>
<style >
            
.advance-fee .tab-main {
  padding-top: 39px !important;
}

</style>

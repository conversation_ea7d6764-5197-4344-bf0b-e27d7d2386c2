<template>
  <page class="selfRefund">
    <cd-search
      v-model="rData"
      :url="'getRefundAuditList'"
      :rowkey="'uid'"
      :addcolumn="'__action'"
      :columns="columns"
      @resetClick="$resetClick"
      ref="search">
      <div slot="hearderTop">
        <p class="co-tip">{{ $lang(`温馨提示：与门店名称组合查询时，订单号支持后六位查询`) }}</p>
      </div>
      <template slot="forms">
        <!-- <cd-form-item
          :label="$lang(`品牌名称`)"
          prop="brandName">
          <cd-input
            @input="$doSearchByKeyWord('brand', arguments)"
            @select="$doSelectByKeyWord('brand', arguments)"
            @blur="$doClearList"
            :inputlistdata="searchBrandData"
            @focus="$doSearchByKeyWord('brand', arguments)"
            v-model="brandName"
            :listKey="'brandName'">
            <template
              slot="itemText"
              scope="props">
              {{ props.item.brandName }}
            </template>
          </cd-input>
        </cd-form-item> -->
        <cd-form-item
          :label="$lang(`门店名称`)"
          prop="storeName">
          <cd-input
            @input="$doSearchByKeyWord('store', arguments)"
            @select="$doSelectByKeyWord('store', arguments)"
            @blur="$doClearList"
            :inputlistdata="searchStoreData"
            @focus="$doSearchByKeyWord('store', arguments)"
            v-model="storeName"
            :listKey="'storeName'">
            <template
              slot="itemText"
              scope="props">
              {{ props.item.storeName }}
            </template>
          </cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`联系电话`)"
          class="phone"
          prop="phone">
          <cd-input v-model="rData.phone"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`系统订单号`)"
          prop="orderNo">
          <cd-input v-model="rData.orderNo"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`退款流水号`)"
          prop="refundNo">
          <cd-input v-model="rData.refundNo"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`下单渠道`)"
          prop="fromType">
          <cd-select
            :options="fromTypeList"
            v-model="rData.fromType"></cd-select>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`退款状态`)"
          prop="refundStatus">
          <cd-select
            :options="refundStatusList"
            v-model="rData.refundStatus"></cd-select>
        </cd-form-item>
        <cd-form-item>
          <cd-Date
            :startTime="rData.startTime"
            :endTime="rData.endTime"
            :label1="$lang(`退款时间：`)"
            label2="-"
            @focus="$dataChange"></cd-Date>
        </cd-form-item>
      </template>
      <template
        slot="__action"
        scope="props">
        <cd-button
          type="b3"
          @click="$getOrderDetail(props.record)">
          {{ $lang(`查看`) }}
        </cd-button>
        <cd-button
          type="b3"
          v-if="[0, 3].includes(props.record.status) && codeConfig.manualRefund.hasRead"
          @click="$refused(props.record.auditId)">
          {{ $lang(`手动退款`) }}
        </cd-button>
        <cd-button
          type="b3"
          v-if="props.record.status == 3"
          @click="$processed(props.record.auditId)">
          {{ $lang(`已处理`) }}
        </cd-button>
      </template>
    </cd-search>
    <div v-if="isLookover">
      <cd-lookover
        @close="$colseModal"
        :rData="orderListDetail"
        :changeOrderStatus="false"
        v-if="rDataObj"></cd-lookover>
    </div>
    <div
      class="showDialog"
      v-if="showModal">
      <cd-modal
        class
        sm
        :show="dealDialog"
        @close="$closeDealModal"
        :title="$lang(`备注`)"
        :btnOkText="$lang('提交')"
        @callback="$doDealSave">
        <template slot="modal-body">
          <div>
            <span class="mb10 din">{{ $lang(`对该订单进行已处理标记，并不会改变退款状态，但可记录当前备注。该操作不可逆转，请确认无误后再进行操作`) }}</span>
            <cd-form :model="changeStatusData">
              <cd-form-item label>
                <cd-input
                  type="textarea"
                  v-model="changeStatusData.textareaInput"
                  :placeholder="$lang(`请输入备注内容`)"
                  :width="600"
                  :height="68"></cd-input>
              </cd-form-item>
            </cd-form>
          </div>
        </template>
      </cd-modal>
    </div>
  </page>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import cdSearch from 'project/index/components/Search';
import Page from 'project/index/components/Page';
import lookover from '../../order/orderList/modal-lookover.vue';
import { eventBus } from 'build/plugs/utils';
let searchTimer;
const columns = [{
  dataIndex: '__action',
  title: i18n.tt("操作"),
  width: 280
},
// {
//   dataIndex: 'brandName',
//   title: i18n.tt("品牌名称 "),
//   width: 180
// },
{
  dataIndex: 'storeName',
  title: i18n.tt("门店名称 "),
  width: 180
}, {
  dataIndex: 'refundMoney',
  title: i18n.tt("退款金额 "),
  width: 100
}, {
  dataIndex: 'status',
  title: i18n.tt("退款状态 "),
  width: 140,
  Fn(item, data) {
    let str = '';
    if (item == 0) {
      str = i18n.tt("待审核");
    } else if (item == 1) {
      str = i18n.tt("发起退款");
    } else if (item == 2) {
      str = i18n.tt("完成退款");
    } else if (item == 3) {
      str = i18n.tt("退款失败");
    }
    return str;
  }
}, {
  dataIndex: 'msg',
  title: i18n.tt("描述 "),
  width: 150
}, {
  dataIndex: 'remark',
  title: i18n.tt("备注 "),
  width: 150
}, {
  dataIndex: 'operatorName',
  title: i18n.tt("操作人 "),
  width: 150
}, {
  dataIndex: 'phone',
  title: i18n.tt("联系电话 "),
  width: 100
}, {
  dataIndex: 'orderNo',
  title: i18n.tt("系统订单号 "),
  width: 180
}, {
  dataIndex: 'refundNo',
  title: i18n.tt("退款流水号 "),
  width: 250
}, {
  dataIndex: 'createTime',
  title: i18n.tt("申请时间 "),
  width: 180
}, {
  dataIndex: 'orderCreateTime',
  title: i18n.tt("下单时间"),
  width: 180
}, {
  dataIndex: 'onlinePayType',
  title: i18n.tt("支付渠道 "),
  width: 140,
  Fn(item, data) {
    let str = '-';
    if (!item) return '-';
    if (item && item.length) {
      if (item[0] == 1) {
        str = i18n.tt("支付宝");
      } else if (item[0] == 2) {
        str = i18n.tt("微信");
      }
    }
    return str;
  }
}];
export default {
  name: 'finance-selfRefund',
  mixins: [eventBus],
  data() {
    return {
      columns: columns,
      rData: {
        phone: '',
        orderNo: null,
        fromType: 0,
        refundStatus: -1,
        startTime: Com.dateFormat(new Date(), 'yyyy-MM-dd') //时间开始
        ,
        endTime: Com.dateFormat(new Date(), 'yyyy-MM-dd') //时间结束
        ,
        brandId: 0,
        storeId: 0,
        refundNo: null
      }
      //0-待审核，1-发起退款，2-完成退款, 3-退款失败
      ,
      refundStatusList: [{
        value: -1,
        label: i18n.tt("全部")
      }, {
        value: 0,
        label: i18n.tt("待审核")
      }, {
        value: 1,
        label: i18n.tt("发起退款")
      }, {
        value: 2,
        label: i18n.tt("完成退款")
      }, {
        value: 3,
        label: i18n.tt("退款失败")
      }],
      fromTypeList: [],
      brandName: '',
      storeName: '',
      searchBrandData: [],
      searchStoreData: [],
      orderListDetail: [],
      rDataObj: true,
      isLookover: false /* 弹框默认false */,
      showModal: false,
      dealDialog: false,
      detailId: 0,
      changeStatusData: {
        textareaInput: ''
      },
      codeConfig: {
        'manualRefund': {
          code: '00050008',
          name: i18n.tt("手动退款"),
          hasRule: true,
          hasRead: true
        }
      }
    };
  },
  watch: {
    '$store.state.brandData' (data) {
      if (!data.isSingleToSingle && !data.isAllToSingle) return
      this.$refs.search.$resetClick();
    },
  },
  created() {
    // 按钮权限
    for (let key in this.codeConfig) {
      let {
        hasRule,
        hasRead
      } = Com.$confirmFunctionBtn(this.codeConfig[key].code);
      this.codeConfig[key].hasRule = hasRule;
      this.codeConfig[key].hasRead = hasRead;
    }
    this.getTypeList();
  },
  methods: {
    handlePushDateBlur(event, key) {
      // 选择日期
      let date = event.target.value;
      this.rData[key] = date;
    },
    $resetClick() {
      // this.rData.startTime = Com.dateFormat(new Date(),'yyyy-MM-dd');
      // this.rData.endTime = Com.dateFormat(new Date(),'yyyy-MM-dd');
      this.brandName = '';
      this.storeName = '';
    },
    $dataChange(e) {
      this.rData.startTime = e.startTime;
      this.rData.endTime = e.endTime;
      console.log(8585, e.startTime, e.endTime);
    },
    $setPhone() {
      if (this.rData.phone) {
        let phone = this.rData.phone.toString();
        if (phone.length > 11) {
          this.rData.phone = phone.substr(0, 11);
        }
      }
    },
    getProduct(config) {
      Com.vm.$service.get('getProduct', config).then(result => {
        if (!result.$.checkResult(result, true)) {
          return;
        }
        this.loading = false;
      });
    },
    $refused(id) {
      if (!this.codeConfig.manualRefund.hasRule) {
        Com.showRuleTip();
        return;
      }
      Modal.confirm({
        content: i18n.tt("是否确定同意该笔退款申请？"),
        onOk: () => {
          let config = {
            auditId: id
          };
          Com.vm.$service.get('acceptRequest', config).then(result => {
            if (!result.$.checkResult(result, true)) {
              return;
            }
            // this.$bus.$emit('refresh');
            Modal.alert({
              title: i18n.tt("提示"),
              content: i18n.tt("系统正在处理退款申请，请稍后查看「退款状态」和「描述」"),
              btnOkText: i18n.tt("确认"),
              onOk: () => {}
            });
            this.$refs.search.$refresh();
          });
        }
      });
    }

    // 刷新列表
    ,
    flushList() {
      this.$refs.search.$refresh();
    }
    // 已处理
    ,
    $processed(ids) {
      this.detailId = ids;
      this.showModal = true;
      this.dealDialog = true;
    }
    // 已处理弹框按钮保存
    ,
    $doDealSave() {
      if (!this.changeStatusData.textareaInput) {
        Com.$messager(i18n.tt("备注不能为空"));
      } else {
        let params = {
          auditId: this.detailId,
          remark: this.changeStatusData.textareaInput
        };
        Com.vm.$service.get('manualRefundProcess', params).then(result => {
          if (result.$.checkResult(result, true)) {
            this.$closeDealModal();
            this.flushList();
          }
        });
      }
    }
    // 已处理弹框关闭按钮
    ,
    $closeDealModal() {
      this.dealDialog = false;
      this.changeStatusData.textareaInput = "";
      this.$nextTick(() => {
        this.showModal = false;
      });
    },
    getTypeList() {
      let vm = this,
        item = {
          value: null,
          label: i18n.tt("全部")
        };
      vm.fromTypeList = [];
      vm.fromTypeList.push(item);
      Com.base.publicInfo.platFromType.forEach(list => {
        item = {
          value: list.value,
          label: list.name
        };
        vm.fromTypeList.push(item);
      });
    },
    $doSearchByKeyWord(key, param) {
      if (key == 'brand') {
        this.rData.brandId = 0;
      } else if (key == 'store') {
        this.rData.storeId = 0;
      }
      let keyWord = param[0];
      keyWord && (this.keyWord = keyWord);
      if (keyWord.trim()) {
        if (key == 'brand') {
          this.searchBrandData = [{
            brandName: i18n.tt("搜索中，请稍候.."),
            brandId: 0
          }];
        } else if (key == 'store') {
          this.searchStoreData = [{
            storeName: i18n.tt("搜索中，请稍候.."),
            storeId: 0
          }];
        }
        if (searchTimer) {
          clearTimeout(searchTimer);
        }
        let data = {
          pageNow: 1,
          pageSize: 9999
        };
        let url = '';
        let tip = '';
        if (key == 'brand') {
          data.brandName = keyWord;
          url = 'findBrandList';
          tip = i18n.tt("查无该品牌，请重新输入！");
        } else if (key == 'store') {
          data.storeName = keyWord;

          //url = 'findStoreList';
          url = 'findStoreListStoreIdAndName';
          tip = i18n.tt("查无该门店，请重新输入！");
        }
        searchTimer = setTimeout(() => {
          this.$service.get(url, data, {
            isHideLoading: true
          }).then(result => {
            if (!result.$.checkResult(result, true)) {
              return;
            }
            let searchData = [];

            // if(result.data.rows.length){
            //     searchData = result.data.rows;
            // }else{
            //     if(key == 'brand'){
            //         this.searchBrandData = [{brandName:tip,brandId:0}];
            //     }else if(key=='store'){
            //         this.searchStoreData = [{storeName:tip,storeId:0}];
            //     }
            //     return;
            // }
            if (key == 'brand') {
              if (result.data.rows.length) {
                searchData = result.data.rows;
              } else {
                this.searchBrandData = [{
                  brandName: tip,
                  brandId: 0
                }];
                return;
              }
            } else if (key == 'store') {
              if (result.data.length) {
                searchData = result.data;
              } else {
                this.searchStoreData = [{
                  storeName: tip,
                  storeId: 0
                }];
                return;
              }
            }
            if (key == 'brand') {
              this.searchBrandData = [].concat(searchData);
            } else if (key == 'store') {
              this.searchStoreData = [].concat(searchData);
            }
          });
        }, 300);
      }
    },
    $doSelectByKeyWord(key, param) {
      let item = param[0];
      let name = '';
      if (key == 'brand') {
        if (item.brandId) {
          name = item.brandName;
        } else {
          this.searchBrandData = [];
        }
        this.$set(this, 'brandName', name);
        this.rData.brandId = item.brandId;
      } else if (key == 'store') {
        if (item.storeId) {
          name = item.storeName;
        } else {
          this.searchStoreData = [];
        }
        this.$set(this, 'storeName', name);
        this.rData.storeId = item.storeId;
      }
    },
    $doClearList(key) {
      if (key == 'brand') {
        this.searchBrandData = [];
      } else if (key == 'store') {
        this.searchStoreData = [];
      }
    },
    $getDetail(orderIds) {
      //查看详情
      console.log(orderIds, 'orderData');
      this.orderListDetail = [];
      this.orderListDetail.push(orderIds.orderId);
      this.isLookover = true;
    },
    $colseModal(data) {
      this.isLookover = false;
    },
    $getOrderDetail(order) {
      this.$bus.$emit('add-tabItem', {
        code: 'order-orderDetail',
        name: `${order.storeName || ''}+${order.orderId || ''}`,
        url: `index/order/orderDetail?orderId=${order.orderId}&changeOrderStatus=false&isStoreRight=true`
      });
    }
  },
  components: {
    cdSearch,
    Page,
    cdLookover: lookover
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {});
  }
}
</script>
<style scoped>
            
.input1 {
  width: 110px;
  height: 30px;
  padding: 6px 12px;
  background: #ffffff;
}

</style>

<style >
            


</style>

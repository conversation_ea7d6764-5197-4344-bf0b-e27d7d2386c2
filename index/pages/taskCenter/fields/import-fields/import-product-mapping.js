import { i18n } from "project/index/libs/i18n";
/*
 * @Author: 
 * @Date: 2022-04-21 21:12:20
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-06-07 17:04:37
 * @FilePath: \candao-ocrm\index\pages\taskCenter\fields\import-fields\import-product-mapping.js
 * @Description: 导入餐品 支持导入映射码 数据模板
 * 
 * Copyright (c) 2022 by 用户/公司名, All Rights Reserved. 
 */
/**
 * 标签-导入标签的表头和处理后的表格数据
 */
import { errorFn, $fmt_error_field } from '../com.js';
import { store } from 'project/index/store';
const error_field = {
  1: {
    type: 1,
    title: i18n.tt("品牌ID"),
    field: "brandId"
  },
  // 2: { type: 2, title: '门店ID ', field: "storeId" },
  3: {
    type: 3,
    title: i18n.tt("餐品映射码"),
    field: "mappingCode"
  },
  4: {
    type: 4,
    title: i18n.tt("餐品映射名称"),
    field: "mappingExtName"
  },
  5: {
    type: 5,
    title: i18n.tt("特殊要求名称"),
    field: "propertyItemNames"
  },
  6: {
    type: 6,
    title: i18n.tt("特殊要求映射名称"),
    field: "propertyItemExtNames"
  },
  7: {
    type: 7,
    title: i18n.tt("特殊要求映射码"),
    field: "propertyItemCodes"
  },
  8: {
    type: 8,
    title: i18n.tt("映射渠道"),
    field: "posName"
  },
  9: {
    type: 9,
    title: i18n.tt("餐品ID"),
    field: "pid"
  },
  10: {
    type: 10,
    title: i18n.tt("餐品名称"),
    field: "productName"
  }
};
const successColumns = [{
  dataIndex: 'brandId',
  title: i18n.tt("品牌ID"),
  width: 100
},
// { dataIndex: 'storeId', title: '门店ID ', width: 200 },
{
  dataIndex: 'mappingCode',
  title: i18n.tt("餐品映射码"),
  width: 200
}, {
  dataIndex: 'mappingExtName',
  title: i18n.tt("餐品映射名称 "),
  width: 200
}, {
  dataIndex: 'propertyItemNames',
  title: i18n.tt("特殊要求名称"),
  width: 200
}, {
  dataIndex: 'propertyItemExtNames',
  title: i18n.tt("特殊要求映射名称"),
  width: 200
}, {
  dataIndex: 'propertyItemCodes',
  title: i18n.tt("特殊要求映射码"),
  width: 200
}, {
  dataIndex: 'posName',
  title: i18n.tt("映射渠道"),
  width: 200
}, {
  dataIndex: 'pid',
  title: i18n.tt("餐品ID"),
  width: 200
}, {
  dataIndex: 'productName',
  title: i18n.tt("餐品名称"),
  width: 200
}];
const failColumns = [{
  dataIndex: 'brandId',
  title: i18n.tt("品牌ID"),
  width: 100,
  Fn: errorFn
},
// { dataIndex: 'storeId', title: '门店ID ', width: 200, Fn: errorFn },
{
  dataIndex: 'mappingCode',
  title: i18n.tt("餐品映射码"),
  width: 200,
  Fn: errorFn
}, {
  dataIndex: 'mappingExtName',
  title: i18n.tt("餐品映射名称 "),
  width: 200,
  Fn: errorFn
}, {
  dataIndex: 'propertyItemNames',
  title: i18n.tt("特殊要求名称"),
  width: 200,
  Fn: errorFn
}, {
  dataIndex: 'propertyItemExtNames',
  title: i18n.tt("特殊要求映射名称"),
  width: 200,
  Fn: errorFn
}, {
  dataIndex: 'propertyItemCodes',
  title: i18n.tt("特殊要求映射码"),
  width: 200,
  Fn: errorFn
}, {
  dataIndex: 'posName',
  title: i18n.tt("映射渠道"),
  width: 200,
  Fn: errorFn
}, {
  dataIndex: 'pid',
  title: i18n.tt("餐品ID"),
  width: 200,
  Fn: errorFn
}, {
  dataIndex: 'productName',
  title: i18n.tt("餐品名称"),
  width: 200,
  Fn: errorFn
}];
export function getImportProductMappingFields(type, baseData) {
  let failList = [];
  let successList = [];
  if (baseData) {
    failList = baseData.failList;
    successList = baseData.successList;
  } else {
    failList = store.state.taskCenter.failList;
    successList = store.state.taskCenter.successList;
  }
  let failDataList = $fmt_error_field(failList, error_field);
  let successDataList = successList;
  return {
    columns_success: successColumns,
    columns_error: failColumns,
    failDataList,
    successDataList
  };
}
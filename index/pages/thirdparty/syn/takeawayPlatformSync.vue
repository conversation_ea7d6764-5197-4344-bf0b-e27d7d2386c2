<template>
  <page
    class="listbox"
    style="margin-top: -20px">
    <search
      ref="seach"
      v-model="rData"
      :url="'getSyncThreeList'"
      :isFilter="true"
      :isChangeField="true"
      :isScreen="isScreen"
      @screen="$screen"
      :addcolumn="'__action'"
      :columns="columns"
      :configColumns="configColumns"
      @modifyColumns="$modifyColumns"
      @search="$search"
      @refresh='$search'
      @resetClick="$resetClick"
      :rowkey="'id'"
      :rowSelection="rowSelection"
      :slotColumns="['mappingCode', 'lastUpdateRemark']"
      @table-checked="$tableChecked">
      <template slot="hearderTop">
        <batch-manage
          :isBatchs="isBatchs"
          @change="$doClick"
          :btnArr="arr"
          class="mr10"
          :hasRule="codeConfig.batchManage.hasRule"
          v-if="codeConfig.batchManage.hasRead"></batch-manage>
        <cd-button
          type="b2"
          @click="changeLinkTiming"
          v-if="codeConfig.timingSyn.hasRead">
          {{ $lang(`定时同步`) }}
        </cd-button>
        <cd-button
          type="b2"
          @click="changeLinkHistory"
          v-if="codeConfig.historySyn.hasRead">
          {{ $lang(`历史同步记录`) }}
        </cd-button>
        <cd-button
          type="b2"
          @click="clickProMapping"
          v-if="codeConfig.proMapping.hasRead">
          {{ $lang(`平台餐品映射`) }}
        </cd-button>
        <cd-button
          type="b2"
          @click="clickProAuditResult"
          v-if="codeConfig.proAuditResult.hasRead">
          {{ $lang(`商品审核结果`) }}
        </cd-button>
      </template>
      <span slot="forms">
        <!-- <cd-form-item
          :label="$lang(`品牌名称`)"
          prop="brandName">
          <cd-input
            @input="$doSearchByKeyWord('brand', arguments)"
            @select="$doSelectByKeyWord('brand', arguments)"
            @blur="$doClearList"
            :inputlistdata="searchBrandData"
            @focus="$doSearchByKeyWord('brand', arguments)"
            v-model="brandName"
            :listKey="'brandName'">
            <template
              slot="itemText"
              scope="props">
              {{ props.item.brandName }}
            </template>
          </cd-input>
        </cd-form-item> -->
        <cd-form-item :label="$lang(`门店名称`)">
          <select-continuity
            :valueKey="'storeId'"
            :labelKey="'storeName'"
            v-model="storeNames"
            :currentModel="`storeNames`"
            @checkselect="$checkselect"
            @cleanAll="cleanAll"
            url="findStoreListStoreIdAndName"
            :tipName="$lang(`门店`)"
            :selectData="false"
            :cleanAllClick="cleanAllClick"></select-continuity>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`门店ID`)"
          prop="sid">
          <cd-input-number
            v-model="rData.sid"
            @input="$inputStore"></cd-input-number>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`门店映射码`)"
          prop="mappingCode">
          <cd-input
            v-model="rData.storeMappingCode"
            type="text"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`外卖平台`)"
          prop="fromTypeIds"
          class="pr">
          <selectCheck
            :allText="$lang(`全部`)"
            :multipleSelectedText="$lang(`部分外卖平台`)"
            :valueKey="'value'"
            :labelKey="'label'"
            :returnAllValue="false"
            v-model="rData.fromTypeIds"
            :clientTypeList="channels"></selectCheck>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`同步状态`)"
          prop="sysStoreStatus">
          <cd-select
            :options="syncStatus"
            v-model="rData.syncStatus"></cd-select>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`异常记录`)"
          prop="hasAbnormal">
          <cd-select
            :options="hasAbnormal"
            v-model="rData.hasAbnormal"
            @input="$inputHasAbnormal"></cd-select>
        </cd-form-item>
        <cd-form-item
          label
          prop>
          <select-org-btn
            ref="refOrg"
            :parentData="rData"></select-org-btn>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`餐单分类`)"
          prop="name">
          <cd-input
            @input="$doSearchByKeyWord('categoty', arguments)"
            @select="$doSelectByKeyWord('categoty', arguments)"
            @blur="$doClearList"
            :inputlistdata="searchCategotyData"
            @focus="$doSearchByKeyWord('categoty', arguments)"
            v-model="name"
            :listKey="'name'">
            <template
              slot="itemText"
              scope="props">
              {{ (props.item.uid ? props.item.uid + "-" : "") + props.item.name }}
            </template>
          </cd-input>
        </cd-form-item>
        <cd-form-item>
          <cd-Date
            :startTime="rData.startTime"
            :endTime="rData.endTime"
            :label1="$lang(`最后同步时间：`)"
            label2="-"
            @focus="$dataChange"></cd-Date>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`门店异常状态`)"
          v-show="isScreen"
          prop="storeAbnormal">
          <cd-select
            :options="storeAbnormal"
            v-model="storeAbnormalV"
            @input="$inputStoreAbnormal"></cd-select>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`平台门店状态`)"
          v-show="isScreen"
          prop="platStoreStatus">
          <cd-select
            :options="platStoreStatus"
            v-model="rData.platStoreStatus"></cd-select>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`系统门店状态`)"
          v-show="isScreen"
          prop="sysStoreStatus">
          <cd-select
            :options="sysStoreStatus"
            v-model="rData.sysStoreStatus"></cd-select>
        </cd-form-item>
        <cd-form-item>
          <cd-region
            @change="$changeRegion"
            inline
            hasLabel
            v-show="isScreen"
            v-model="region"></cd-region>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`区域`)"
          prop="districtYHDW"
          v-show="isScreen && isShowdistrictYHDW">
          <cd-input
            v-model="rData.districtYHDW"
            type="text"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`门店属性`)"
          prop="storeProperty"
          v-show="isScreen && showStoreProperty">
          <cd-input
            v-model="rData.storeProperty"
            type="text"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`门店标签1`)"
          prop="remarkOne"
          v-show="isScreen">
          <cd-input
            v-model="rData.remarkOne"
            type="text"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`门店标签2`)"
          prop="remarkTwo"
          v-show="isScreen">
          <cd-input
            v-model="rData.remarkTwo"
            type="text"></cd-input>
        </cd-form-item>
        <cd-form-item
          :label="$lang(`门店标签3`)"
          prop="remarkThree"
          v-show="isScreen">
          <cd-input
            v-model="rData.remarkThree"
            type="text"></cd-input>
        </cd-form-item>
      </span>
      <template
        slot="__action"
        scope="props">
        <div>
          <cd-button
            type="b3"
            :disabled="props.record.type == 1"
            @click="$showEditSyn(props, false)"
            v-if="codeConfig.syn.hasRead">
            {{ $lang(`同步`) }}
          </cd-button>
          <cd-button
            type="b3"
            @click="$showSynRecord(props)"
            v-if="codeConfig.synRecord.hasRead">
            {{ $lang(`同步记录`) }}
          </cd-button>
        </div>
      </template>
      <template
        slot="mappingCode"
        scope="props">
        <MappingCodeItem :item="props.record"></MappingCodeItem>
      </template>
      <template
        slot="lastUpdateRemark"
        scope="props">
        <RsRemark
          :item="props.record"
          :itemKey="'lastUpdateRemark'"></RsRemark>
      </template>
    </search>
    <div v-if="showEditSyn">
      <edit-syn
        @close="$closeModal"
        @save="$saveModal"
        :rData="synData"
        :synchronousFrom="synchronousFrom"
        :cookEnable="cookEnable"
        :isInBatch="isInBatch"
        :fromType="fromType"
        :mtQYTipShow="mtQYTipShow"></edit-syn>
    </div>
    <div v-if="showSynRecord">
      <syn-record
        @close="$closeModal"
        @save="$saveModal"
        :reData="recordData"></syn-record>
    </div>
    <scheduledTasks
      v-if="scheduledTasks"
      :whetherShowType="false"
      :rData="scheduledData"
      @save="$saveModal"
      @close="$closeModal"></scheduledTasks>
  </page>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import Search from 'project/index/components/Search';
import Region from 'project/index/components/Region';
import { eventBus } from 'build/plugs/utils';
import editSyn from './modal-editSyn.vue';
import synRecord from './modal-synRecord.vue';
import batchManage from 'project/index/components/batchManage';
import Page from 'project/index/components/Page';
import SelectContinuity from "project/index/components/SelectContinuity";
import scheduledTasks from '../channelSyncTask/modal-scheduledTasks.vue';
import selectCheck from 'project/index/components/selectCheck';
import MappingCodeItem from 'project/index/components/MappingCodeItem';
import RsRemark from 'project/index/components/RsRemarkItem';
import { SelectOrgBtn } from 'project/index/components/OrganizationTree';
import thirdUtil from 'project/index/utils/third/thirdUtil.js';
let searchTimer;
const columns = [{
  dataIndex: '__action',
  title: i18n.tt("操作"),
  width: 220,
  sorter: true
}, {
  dataIndex: 'syncStatus',
  title: i18n.tt("同步状态 "),
  width: i18n.current === "en" ? 120 : 62,
  sorter: true,
  Fn(item, data) {
    let str = '';
    if (item == 1) {
      str = "<span style=\"color:green\">" + i18n.tt("同步成功") + "</span>";
    } else if (item == 2) {
      str = "<span style=\"color:red\">" + i18n.tt("同步失败") + "</span>";
    } else if (item == 3) {
      str = "<span>" + i18n.tt("同步中") + "</span>";
    }
    return str;
  }
}, {
  dataIndex: 'failSolution',
  title: i18n.tt("建议解决方案 "),
  width: 140,
  sorter: true
}, {
  dataIndex: 'hasAbnormal',
  title: i18n.tt("异常记录 "),
  width: 60,
  sorter: true,
  Fn(item, data) {
    let str = '';
    if (item) {
      str = i18n.tt("是");
    } else {
      str = i18n.tt("否");
    }
    return str;
  }
},
// {
//   dataIndex: 'brandName',
//   title: i18n.tt("品牌名称 "),
//   width: 100,
//   sorter: true
// },
{
  dataIndex: 'storeName',
  title: i18n.tt("门店名称 "),
  width: 100,
  sorter: true
}, {
  dataIndex: 'storeId',
  title: i18n.tt("门店ID "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'mappingCode',
  title: i18n.tt("门店映射码 "),
  width: 160,
  sorter: true
},
// {dataIndex: 'name', title: '餐单分类 ', width: 60, sorter: true}, // 列表不需要显示
{
  dataIndex: 'fromTypeName',
  title: i18n.tt("外卖平台 "),
  width: i18n.current === "en" ? 120 : 60,
  sorter: true
}, {
  dataIndex: 'lastUpdateRemark',
  title: i18n.tt("最后同步备注 "),
  width: 188,
  sorter: true
}, {
  dataIndex: 'updateTime',
  title: i18n.tt("最后同步时间 "),
  width: 188,
  sorter: true
}, {
  dataIndex: 'provinceName',
  title: i18n.tt("省份 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'cityName',
  title: i18n.tt("城市 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'districtName',
  title: i18n.tt("行政区域 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'address',
  title: i18n.tt("门店地址 "),
  width: 180,
  sorter: true
}, {
  dataIndex: 'storeAbnormal',
  title: i18n.tt("门店异常状态 "),
  width: 100,
  sorter: true,
  Fn(item, data) {
    let str = '';
    if (item) {
      str = i18n.tt("正常");
    } else {
      str = i18n.tt("异常");
    }
    return str;
  }
}, {
  dataIndex: 'platStoreStatus',
  title: i18n.tt("平台门店状态 "),
  width: 100,
  sorter: true,
  Fn(item, data) {
    let str = '';
    if (item == 1) {
      str = i18n.tt("营业");
    } else if (item == 2) {
      str = i18n.tt("休息");
    } else if (item == 3) {
      str = i18n.tt("下线");
    } else if (item == 4) {
      str = i18n.tt("初始化值");
    }
    return str;
  }
}, {
  dataIndex: 'sysStoreStatus',
  title: i18n.tt("系统门店状态 "),
  width: 100,
  sorter: true,
  Fn(item, data) {
    let str = '';
    if (item == 1) {
      str = i18n.tt("营业");
    } else if (item == 2) {
      str = i18n.tt("休息");
    } else if (item == 3) {
      str = i18n.tt("停用");
    }
    return str;
  }
}, {
  dataIndex: 'remarkOne',
  title: i18n.tt("门店标签1"),
  width: 100,
  sorter: true
}, {
  dataIndex: 'remarkTwo',
  title: i18n.tt("门店标签2"),
  width: 100,
  sorter: true
}, {
  dataIndex: 'remarkThree',
  title: i18n.tt("门店标签3"),
  width: 100,
  sorter: true
}];
function xdId() {
  if (Com.base.isRelease) {
    //正式
    return 405;
  } else if (Com.base.isBeta) {
    //beta
    return 435;
  } else if (Com.base.isQC) {
    //qc
    return 431;
  } else {
    //本地
    return 431;
  }
}
export default {
  name: 'takeawayPlatformSync',
  mixins: [eventBus],
  data() {
    return {
      columns: Array.from(columns) //表格列的配置描述
      ,

      configColumns: [],
      rowSelection: {
        // getCheckboxProps (record) {
        // },
        onChange(selectedRowKeys, selectedRows) {
          // console.log(selectedRowKeys, selectedRows,'1')
        },
        onSelect(record, selected, selectedRows) {
          // console.log(record, selected, selectedRows,'2')
        },
        onSelectAll(selected, selectedRows, changeRows) {
          // console.log(selected, selectedRows, changeRows,'3')
        }
      },
      rData: {
        // brandName:''
        // ,storeName:''
        brandId: 0,
        storeIds: [],
        storeId: 0,
        sid: null,
        synchronousFrom: 0,
        fromTypeIds: [],
        hasAbnormal: null,
        storeAbnormal: null,
        platStoreStatus: 0,
        sysStoreStatus: 0,
        provinceId: 0 //省份id
        ,
        cityId: 0 //城市id
        ,
        districtId: 0 //区域id
        ,
        startTime: null //7天前//推送时间 开始时间
        ,
        endTime: null //推送时间  结束时间
        ,
        syncStatus: 0,
        districtYHDW: '' //区域
        ,
        categotyId: 0 // 餐单分类id
        ,
        storeProperty: '' // 门店属性
        ,
        storeMappingCode: '',
        oIds: [],
        remarkOne: "",
        remarkTwo: "",
        remarkThree: ""
      },
      brandName: '',
      name: '' // 餐单分类名称
      ,

      storeName: '',
      searchBrandData: [],
      searchStoreData: [],
      searchCategotyData: [],
      // channels: [{value: 0, label: '全部'}]  //外卖平台
      channels: [] //外卖平台
      ,

      hasAbnormal: [{
        value: null,
        label: i18n.tt("全部")
      }, {
        value: 1,
        label: i18n.tt("是")
      }, {
        value: 2,
        label: i18n.tt("否")
      }] //异常记录
      ,

      storeAbnormal: [{
        value: null,
        label: i18n.tt("全部")
      }, {
        value: 1,
        label: i18n.tt("异常")
      }, {
        value: 2,
        label: i18n.tt("正常")
      }] //门店异常状态
      ,
      storeAbnormalV: null,
      platStoreStatus: [{
        value: 0,
        label: i18n.tt("全部")
      }, {
        value: 4,
        label: i18n.tt("初始化值")
      }, {
        value: 1,
        label: i18n.tt("营业")
      }, {
        value: 2,
        label: i18n.tt("休息")
      }, {
        value: 3,
        label: i18n.tt("下线")
      }] //平台门店状态
      ,

      sysStoreStatus: [{
        value: 0,
        label: i18n.tt("全部")
      }, {
        value: 1,
        label: i18n.tt("营业")
      }, {
        value: 2,
        label: i18n.tt("休息")
      }, {
        value: 3,
        label: i18n.tt("停用")
      }] //系统门店状态
      ,

      syncStatus: [{
        value: 0,
        label: i18n.tt("全部")
      }, {
        value: 1,
        label: i18n.tt("同步成功")
      }, {
        value: 2,
        label: i18n.tt("同步失败")
      }, {
        value: 3,
        label: i18n.tt("同步中")
      }] //同步状态
      ,

      isBatchEdit: false,
      batchEdit: {
        isSureEdit: false,
        cb: function () {}
      },
      showMore: false //显示更多，true显示省份，城市，行政区域这三个select框，反之隐藏
      ,

      showEditSyn: false //同步弹窗的显示与否
      ,

      synData: {
        //同步按钮需要传过去的数据
        ids: [] //同步按钮需要传过去的id
        ,
        brandId: [],
        storeList: [],
        fromType: 0,
        brandName: "",
        fromTypeName: "",
        isBatch: false
      },
      selected: [] //批量选中的门店
      ,

      showSynRecord: false //控制同步记录弹窗
      ,

      recordData: {
        //同步记录弹窗需要传过去的数据
        id: ''
      },
      isBatchs: false //批量管理 有数据时，需设置为true
      ,

      arr: [{
        name: i18n.tt("批量同步")
      }],
      isScreen: false //是否高级筛选
      ,

      storeNames: [],
      cleanAllClick: false,
      codeConfig: {
        'batchManage': {
          code: '0004000200010001',
          name: i18n.tt("批量管理"),
          hasRule: true,
          hasRead: true
        },
        'batchSyn': {
          code: '00040002000100010001',
          name: i18n.tt("批量同步"),
          hasRule: true,
          hasRead: true
        },
        'timingSyn': {
          code: '0004000200010002',
          name: i18n.tt("定时同步"),
          hasRule: true,
          hasRead: true
        },
        'historySyn': {
          code: '0004000200010005',
          name: i18n.tt("历史同步记录"),
          hasRule: true,
          hasRead: true
        },
        'proMapping': {
          code: '0004000200010006',
          name: i18n.tt("平台餐品映射"),
          hasRule: true,
          hasRead: true
        },
        'syn': {
          code: '0004000200010003',
          name: i18n.tt("同步"),
          hasRule: true,
          hasRead: true
        },
        'synRecord': {
          code: '0004000200010004',
          name: i18n.tt("同步记录"),
          hasRule: true,
          hasRead: true
        },
        'proAuditResult': {
          code: '0004000200010007',
          name: i18n.tt("商品审核结果"),
          hasRule: true,
          hasRead: true
        }
      },
      synchronousFrom: '',
      //渠道
      isShowdistrictYHDW: false,
      showStoreProperty: false // 根据oprs配置显示门第属性搜索
      ,
      cookEnable: false // 批量同步弹框是否显示出餐时间配置的标志
      ,
      isInBatch: false // 批量同步弹框是否处于批量操作
      ,
      xdId: 0 // 心动外卖posid
      ,
      fromType: null,
      mttcId: 453 // 美团企业团餐id
      ,
      mtQYTipShow: false,
      // 是否存在美团企业团餐
      scheduledTasks: false,
      // pos弹窗
      scheduledData: []
    };
  },
  created() {
    let isShowdistrictYHDW = Com.base.Platform.variableFieldList.every(v => v.cId !== 2018);
    this.isShowdistrictYHDW = !isShowdistrictYHDW;
    if (this.isShowdistrictYHDW) {
      this.columns.push({
        dataIndex: 'districtYHDW',
        title: i18n.tt("区域 "),
        width: 100,
        sorter: true,
        editor: true
      });
    }

    // 门店属性
    let storeProperty = Com.base.Platform.variableFieldList.find(v => v.cId === 2003);
    this.showStoreProperty = storeProperty && Object.keys(storeProperty).length > 0 ? true : false;
    if (this.showStoreProperty) {
      this.columns.push({
        dataIndex: 'storeProperty',
        title: i18n.tt("门店属性 "),
        width: 200,
        sorter: true,
        editor: true
      });
    }

    // 按钮权限
    for (let key in this.codeConfig) {
      let {
        hasRule,
        hasRead
      } = Com.$confirmFunctionBtn(this.codeConfig[key].code);
      this.codeConfig[key].hasRule = hasRule;
      this.codeConfig[key].hasRead = hasRead;
    }
    this.arr.forEach((m, index) => {
      if (index == 0) {
        m.hideItem = !this.codeConfig.batchSyn.hasRead;
      }
    });
    let self = this;
    this.$bus.$on('refreshPage', data => {
      if (data == 8) {
        setTimeout(() => {
          if (self.$refs.seach) {
            self.$refs.seach.$refresh();
          }
        }, 100);

        // console.log('平台推送失败刷新')
      }
    });

    // 请求当前用户保存的展示列表
    Com.vm.$service.get('getFieldSort', {
      type: 9
    }).then(result => {
      if (result.$.checkResult(result, true)) {
        if (result.data) {
          this.sid = result.data.sid; // 保存当前sid，用来更新排序
          let fieldList = result.data.fieldList;
          let allList = [...this.columns.map(item => {
            return item.dataIndex;
          }), ...this.configColumns.map(item => {
            return item.dataIndex;
          })];
          let existList = [];
          let noExistList = [];
          fieldList.forEach(value => {
            let fieldIndex = allList.indexOf(value);
            if (this.columns[fieldIndex]) {
              existList.push(this.columns[fieldIndex]);
            } else if (this.configColumns[fieldIndex - this.columns.length]) {
              existList.push(this.configColumns[fieldIndex - this.columns.length]);
            }
            ;
          });
          allList.forEach((value, index) => {
            if (fieldList.indexOf(value) < 0) {
              this.columns[index] ? noExistList.push(this.columns[index]) : noExistList.push(this.configColumns[index - this.columns.length]);
            }
          });
          this.columns = existList;
          this.configColumns = noExistList;
        }
      }
    });

    // 根据不同环境赋值心动外卖posId
    this.xdId = xdId();
  },
  computed: {
    region() {
      return {
        cityId: this.rData.cityId,
        districtId: this.rData.districtId,
        provinceId: this.rData.provinceId
      };
    }
  },
  watch: {
    '$store.state.brandData' (data) {
      if (!data.isSingleToSingle && !data.isAllToSingle) return
      this.$refs.seach.$resetClick();
    },
  },
  methods: {
    changeLinkTiming() {
      if (!this.codeConfig.timingSyn.hasRule) {
        Com.showRuleTip();
        return;
      }
      this.$bus.$emit('add-tabItem', {
        code: "thirdparty-channelSyncTask",
        url: 'index/thirdparty/channelSyncTask',
        name: i18n.tt("定时同步任务管理")
      });
    },
    changeLinkHistory() {
      if (!this.codeConfig.historySyn.hasRule) {
        Com.showRuleTip();
        return;
      }
      this.$bus.$emit('add-tabItem', {
        code: 'thirdparty-historySyncRecord',
        url: 'index/thirdparty/historySyncRecord',
        name: i18n.tt("历史同步记录")
      });
    },
    clickProMapping() {
      if (!this.codeConfig.proMapping.hasRule) {
        Com.showRuleTip();
        return;
      }
      this.$bus.$emit('add-tabItem', {
        code: 'thirdparty-pro-mapping',
        url: 'index/thirdparty/proMapping',
        name: i18n.tt("平台餐品映射")
      });
    },
    // 商品审核结果
    clickProAuditResult() {
      if (!this.codeConfig.proAuditResult.hasRule) {
        Com.showRuleTip();
        return;
      }
      this.$bus.$emit('add-tabItem', {
        code: 'thirdparty-productAuditResult',
        url: 'index/thirdparty/productAuditResult',
        name: i18n.tt("商品审核结果")
      });
    },
    cleanAll() {
      this.cleanAllClick = false;
    },
    $checkselect(obj) {
      this.rData.storeIds = obj.value;
      console.log(obj.value);
    },
    $resetClick() {
      this.brandName = '';
      this.storeName = '';
      this.name = '';
      this.isBatchs = false;
      this.cleanAllClick = true;
      this.storeAbnormalV = null;
    },
    $search() {
      this.isBatchs = false;
    },
    $tableChecked(checkedRow, checked, checkedRows) {
      this.selected = checkedRows;
      if (checkedRows.length > 0) {
        this.isBatchs = true;
      } else {
        this.isBatchs = false;
      }
    },
    $doClick(index) {
      //批量管理
      if (this.isBatchs) {
        if (index == 0) {
          //批量同步
          if (!this.codeConfig.batchSyn.hasRule) {
            Com.showRuleTip();
            return;
          }
          if (this.selected && this.selected.length) {
            if (this.selected.some(item => item.synchronousFrom != 21)) {
              this.synchronousFrom = '';
              if (this.selected.some(item => item.synchronousFrom != 38)) {
                this.synchronousFrom = '';
              } else {
                this.synchronousFrom = 38;
              }
            } else {
              this.synchronousFrom = 21;
            }
            if (this.selected.filter(item => !(item.type == 0 && (item.synchronousFrom == 20 || item.synchronousFrom == 482))).length > 0) {
              this.cookEnable = false;
            } else {
              this.cookEnable = true;
            }
            // 批量选择的渠道包含【美团企业团餐】则显示提示文案
            this.mtQYTipShow = false;
            if (this.selected.filter(item => item.synchronousFrom === this.mttcId).length > 0) {
              this.mtQYTipShow = true;
            }
            for (let i = 0; i < this.selected.length; i++) {
              const itemI = this.selected[i];
              for (let j = i + 1; j < this.selected.length; j++) {
                const itemJ = this.selected[j];
                if (itemI.type != itemJ.type) {
                  Modal.alert({
                    content: i18n.tt("非常抱歉，批量操作不支持同时同步外卖平台和pos渠道；请区分开两次操作批量同步外卖平台和批量同步pos。"),
                    btnOkText: i18n.tt("确定"),
                    onOk: () => {}
                  });
                  return;
                }
              }
            }
          }
          this.$showEditSyn(this.selected, true);
        }
      }
    },
    $closeModal(e) {
      if (e == 'editSyn') {
        this.showEditSyn = false; //关闭同步弹窗
        this.isInBatch = false; // 设置不处于批量操作
      } else if (e == 'synRecord') {
        this.showSynRecord = false; //关闭同步弹窗
      } else if (e == 'scheduledTasks') {
        this.scheduledTasks = false;
      }
    },
    $saveModal(e) {
      if (e == 'editSyn') {
        this.selected = []; //清空批量选中的
        this.showEditSyn = false; //关闭同步弹窗
        this.isInBatch = false; // 设置不处于批量操作
        this.isBatchs = false;
      } else if (e == 'synRecord') {
        this.isBatchs = false;
      } else if (e == 'scheduledTasks') {
        this.isBatchs = false;
        this.scheduledTasks = false;
      }
      this.$refs.seach.$refresh();
      setTimeout(() => {
        this.$refs.seach.$refresh();
      }, 30000);
    },
    //根据key去重
    deWeightThree(arr, key) {
      let map = new Map();
      for (let item of arr) {
        if (!map.has(item[key])) {
          map.set(item[key], item);
        }
      }
      return [...map.values()];
    },
    $showEditSyn(props, bol) {
      //点击同步按钮执行的操作
      if (!this.codeConfig.syn.hasRule) {
        Com.showRuleTip();
        return;
      }
      // 0-外卖平台同步 1-pos同步
      if (!bol && props.record.type == 1) {
        // pos同步单个
        this.scheduledTasks = true;
        this.scheduledData = props.record;
      } else if (bol && props.some(m => m.type == 1)) {
        // pos同步批量
        this.scheduledTasks = true;
        this.scheduledData = props;
      } else {
        this.isInBatch = bol;
        // bol为true时是批量同步，bol为false时是单个同步
        if (bol) {
          //批量同步
          this.synData.ids = []; //清空之前所选的
          this.synData.brandId = [];
          this.synData.storeList = [];
          this.synData.isBatch = true;
          const _fromType = [];
          const _fromTypeNames = [];
          const brandInfos = [];
          for (let pro of props) {
            this.synData.ids.push(pro.id);
            this.synData.storeList.push({
              storeId: pro.storeId,
              fromType: pro.synchronousFrom,
              fromTypeName: pro.fromTypeName
            });
            if (!_fromType.includes(pro.synchronousFrom)) {
              _fromType.push(pro.synchronousFrom);
              _fromTypeNames.push(pro.fromTypeName);
            }
            if (!this.synData.brandId.includes(pro.brandId)) {
              this.synData.brandId.push(pro.brandId);
              brandInfos.push({
                brandId: pro.brandId,
                brandName: pro.brandName
              });
            }
          }
          // 去重
          // this.synData.brandId = Array.from(new Set(this.synData.brandId));
          this.showEditSyn = true; //显示同步弹窗
          this.synData.fromType = '';
          this.synData.fromTypes = _fromType;
          this.synData.fromTypeNames = _fromTypeNames;
          this.synData.brandInfos = brandInfos;
          this.synData.storeInfos = this.deWeightThree(props, 'storeId').map(({
            storeId,
            storeName,
            brandId,
            brandName
          }) => ({
            storeId,
            storeName,
            brandId,
            brandName
          }));
        } else {
          //单个同步操作
          const {
            storeId,
            storeName,
            brandId,
            brandName
          } = props.record;
          this.synData.isBatch = false;
          this.synData.brandName = brandName;
          this.synData.fromTypeName = props.record.fromTypeName;
          this.synData.ids = []; //清空之前所选的
          this.synData.ids.push(props.record.id); //选中的id放进数组ids
          this.synData.brandId = [];
          this.synData.brandId.push(brandId);
          this.synData.storeList = [];
          this.synData.storeList.push({
            storeId: props.record.storeId,
            fromType: props.record.synchronousFrom,
            fromTypeName: props.record.fromTypeName
          });
          this.synData.fromType = props.record.synchronousFrom;
          this.showEditSyn = true; //显示同步弹窗
          this.synData.storeInfos = [{
            storeId,
            storeName,
            brandId,
            brandName
          }];
        }
        if (props && props.record && props.record.synchronousFrom) {
          this.synchronousFrom = props.record.synchronousFrom || "";
        }
        if (props && props.record) {
          this.fromType = props.record.type;
        }
      }
    },
    $changeRegion(data) {
      //当省份、城市、区域改变时触发
      this.rData.provinceId = data.provinceId; //省份
      this.rData.cityId = data.cityId; //城市
      this.rData.districtId = data.districtId; //区域
      // Com.cloneObjects(this.region2,data);
    },
    $showSynRecord(props) {
      //同步记录按钮执行的事件
      if (!this.codeConfig.synRecord.hasRule) {
        Com.showRuleTip();
        return;
      }
      this.showSynRecord = true;
      this.recordData.id = props.record.id;
    },
    $screen(screen) {
      this.isScreen = screen;
    },
    $inputStore(val) {
      if (val) {
        this.rData.sid = Number(val);
      } else {
        this.rData.sid = null;
      }
    },
    $inputHasAbnormal(value) {
      //异常记录
      if (value == 1) {
        this.rData.hasAbnormal = true;
      } else if (value == 2) {
        this.rData.hasAbnormal = false;
      } else {
        this.rData.hasAbnormal = null;
      }
    },
    $inputStoreAbnormal(value) {
      //门店异常状态
      if (value == 1) {
        this.rData.storeAbnormal = false;
      } else if (value == 2) {
        this.rData.storeAbnormal = true;
      } else {
        this.rData.storeAbnormal = null;
      }
    },
    $doSearchByKeyWord(key, param) {
      if (key == 'brand') {
        this.rData.brandId = 0;
      } else if (key == 'store') {
        this.rData.storeId = 0;
      } else if (key == 'categoty') {
        this.rData.categoryId = 0;
      }
      let keyWord = param[0];
      keyWord && (this.keyWord = keyWord);
      if (keyWord.trim()) {
        if (key == 'brand') {
          this.searchBrandData = [{
            brandName: i18n.tt("搜索中，请稍候.."),
            brandId: 0
          }];
        } else if (key == 'store') {
          this.searchStoreData = [{
            storeName: i18n.tt("搜索中，请稍候.."),
            storeId: 0
          }];
        } else if (key == 'categoty') {
          this.searchCategotyData = [{
            uid: '',
            name: i18n.tt("搜索中，请稍候.."),
            categoryId: 0
          }];
        }
        if (searchTimer) {
          clearTimeout(searchTimer);
        }
        let data = {
          pageNow: 1,
          pageSize: 9999
        };
        let url = '';
        let tip = '';
        if (key == 'brand') {
          data.brandName = keyWord;
          url = 'findBrandList';
          tip = i18n.tt("查无该品牌，请重新输入！");
        } else if (key == 'store') {
          data.storeName = keyWord;
          // url = 'findStoreList';
          url = 'findStoreListStoreIdAndName';
          tip = i18n.tt("查无该门店，请重新输入！");
        } else if (key == 'categoty') {
          data.name = keyWord;
          url = 'queryProductMenuCategory';
          tip = i18n.tt("查无该餐单分类，请重新输入！");
        }
        searchTimer = setTimeout(() => {
          this.$service.get(url, data, {
            isHideLoading: true
          }).then(result => {
            if (!result.$.checkResult(result, true)) {
              return;
            }
            let searchData = [];
            // if (result.data.rows.length) {
            //   searchData = result.data.rows;
            // } else {
            //   if (key == 'brand') {
            //     this.searchBrandData = [{brandName: tip, brandId: 0}];
            //   } else if (key == 'store') {
            //     this.searchStoreData = [{storeName: tip, storeId: 0}];
            //   } else if (key == 'categoty') {
            //     this.searchCategotyData = [{uid:'', name: tip, categoryId: 0}];
            //   }
            //   return;
            // }
            if (key == 'store') {
              if (result.data.length) {
                searchData = result.data;
              } else {
                this.searchStoreData = [{
                  storeName: tip,
                  storeId: 0
                }];
                return;
              }
            } else {
              if (result.data.rows.length) {
                searchData = result.data.rows;
              } else {
                if (key == 'brand') {
                  this.searchBrandData = [{
                    brandName: tip,
                    brandId: 0
                  }];
                } else if (key == 'store') {
                  this.searchStoreData = [{
                    storeName: tip,
                    storeId: 0
                  }];
                } else if (key == 'categoty') {
                  this.searchCategotyData = [{
                    uid: '',
                    name: tip,
                    categoryId: 0
                  }];
                }
                return;
              }
            }
            if (key == 'brand') {
              this.searchBrandData = [].concat(searchData);
            } else if (key == 'store') {
              this.searchStoreData = [].concat(searchData);
            } else if (key == 'categoty') {
              this.searchCategotyData = [].concat(searchData);
            }
          });
        }, 300);
      }
    },
    $doSelectByKeyWord(key, param) {
      let item = param[0];
      let name = '';
      if (key == 'brand') {
        if (item.brandId) {
          name = item.brandName;
        } else {
          this.searchBrandData = [];
        }
        this.$set(this, 'brandName', name);
        this.rData.brandId = item.brandId;
      } else if (key == 'store') {
        if (item.storeId) {
          name = item.storeName;
        } else {
          this.searchStoreData = [];
        }
        this.$set(this, 'storeName', name);
        this.rData.storeId = item.storeId;
      } else if (key == 'categoty') {
        if (item.uid) {
          name = item.name;
        } else {
          this.searchCategotyData = [];
        }
        this.$set(this, 'name', name);
        this.rData.categoryId = item.uid || 0;
      }
    },
    $doClearList(key) {
      if (key == 'brand') {
        this.searchBrandData = [];
      } else if (key == 'store') {
        this.searchStoreData = [];
      } else if (key == 'categoty') {
        this.searchCategotyData = [];
      }
    },
    $dataChange(e) {
      this.rData.startTime = e.startTime;
      this.rData.endTime = e.endTime;
    },
    $modifyColumns(columns) {
      // 保存 当前用户的展示列表
      let fieldList = [];
      let fieldNameList = [];
      columns.forEach(value => {
        fieldList.push(value.dataIndex);
        fieldNameList.push(value.title);
      });
      if (!this.sid) {
        // 新增
        Com.vm.$service.get('addFieldSort', {
          type: 9,
          fieldList,
          fieldNameList
        }).then(result => {
          if (result.$.checkResult(result, true)) {
            this.sid = result.data.sid;
          }
        });
      } else {
        // 更新
        Com.vm.$service.get('updateFieldSort', {
          sid: this.sid,
          fieldList,
          fieldNameList
        }).then(result => {
          if (result.$.checkResult(result, true)) {}
        });
      }
    }
  },
  mounted() {
    let ids = [];
    // 区外卖平台和pos渠道的并集 2021/4/8需求 -smile
    thirdUtil.getThirdChannelList().forEach(item => {
      if (ids.indexOf(item.id) < 0) {
        this.channels.push({
          value: item.id,
          label: item.name
        });
        ids.push(item.id);
      }
    });
  },
  components: {
    Search,
    cdRegion: Region,
    editSyn,
    synRecord,
    batchManage,
    Page,
    SelectContinuity,
    selectCheck,
    MappingCodeItem,
    RsRemark,
    SelectOrgBtn,
    scheduledTasks
  }
}
</script>
<style scoped>
            

</style>

<template>
  <div class="modalSynRecord-wrap">
    <cd-modal
      :show="visible"
      :title="$lang('同步记录')"
      @close="$close">
      <div
        slot="modal-body"
        class="box">
        <search
          ref="search"
          v-model="rData"
          :url="'getSyncThreeHisList'"
          :batchEdit="batchEdit"
          :addcolumn="'__action'"
          :columns="columns"
          :requestDataAdapterFn="$requestDataAdapterFn"
          :slotColumns="['rsRemark']"
          @resetClick="$resetClick"
          :rowkey="'id'">
          <span slot="forms">
            <cd-form-item :label="$lang(`品牌名称`)">
              <span>{{ reData.brandName || "-" }}</span>
            </cd-form-item>
            <cd-form-item :label="$lang(`门店名称`)">
              <span>{{ reData.storeName || "-" }}</span>
            </cd-form-item>
            <cd-form-item :label="$lang(`渠道`)">
              <span>{{ reData.fromTypeName || "-" }}</span>
            </cd-form-item>
            <cd-form-item :label="$lang(`操作类型`)">
              <span>{{ reData.syncTypeName || "-" }}</span>
            </cd-form-item>
            <cd-form-item label>
              <cd-Date
                v-if="showDate"
                :startTime="startTime"
                :endTime="endTime"
                :label1="$lang(`同步时间：`)"
                :label2="$lang(`至`)"
                @focus="$dataChange"></cd-Date>
            </cd-form-item>
          </span>
          <template
            slot="__action"
            scope="props">
            <div v-if="props.record.syncRs == 2 && filterSyncType.includes(props.record.syncType)">
              <cd-button
                type="b3"
                v-if="!props.record.isHandle"
                @click="$syn(props)">
                {{ $lang(`重新同步`) }}
              </cd-button>
              <cd-button
                type="b3"
                v-if="!props.record.isHandle"
                @click="$ignore(props)">
                {{ $lang(`忽略`) }}
              </cd-button>
            </div>
            <cd-button
              type="b3"
              gray
              v-if="props.record.syncRs == 2 && filterSyncType.includes(props.record.syncType) && props.record.isHandle">
              {{ $lang(`已处理`) }}
            </cd-button>
          </template>
          <template
            slot="rsRemark"
            scope="props">
            <RsRemark :item="props.record"></RsRemark>
          </template>
        </search>
      </div>
      <div
        slot="modal-footer"
        class="modal-footer">
        <cd-button
          type="b5"
          gray
          @click="$close">
          {{ $lang(`关闭`) }}
        </cd-button>
      </div>
    </cd-modal>
  </div>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import Modal from 'project/index/components/Modal';
import cdDate from 'project/index/components/Date';
import Search from 'project/index/components/Search';
import { eventBus } from 'build/plugs/utils';
import RsRemark from 'project/index/components/RsRemarkItem';
import batchManage from 'project/index/components/batchManage';
const columns = [{
  dataIndex: '__action',
  title: i18n.tt("操作"),
  width: 200,
  sorter: true
}, {
  dataIndex: 'opTime',
  title: i18n.tt("同步时间 "),
  width: 120,
  sorter: true
}, {
  dataIndex: 'syncTypeName',
  title: i18n.tt("同步操作 "),
  width: 100,
  sorter: true
}, {
  dataIndex: 'opRemark',
  title: i18n.tt("操作备注 "),
  width: 180,
  sorter: true
}, {
  dataIndex: 'syncRs',
  title: i18n.tt("同步结果 "),
  width: 60,
  sorter: true,
  Fn(item, data) {
    let str = '';
    if (item == 1) {
      str = i18n.tt("成功");
    } else if (item == 2) {
      str = i18n.tt("失败");
    } else if (item == 3) {
      str = i18n.tt("同步中");
    }
    return str;
  }
}, {
  dataIndex: 'rsRemark',
  title: i18n.tt("结果备注 "),
  width: 220,
  sorter: true
}, {
  dataIndex: 'fromTypeName',
  title: i18n.tt("外卖平台 "),
  width: 100,
  sorter: true
}, {
  dataIndex: 'isAuto',
  title: i18n.tt("同步类型 "),
  width: 60,
  sorter: true,
  Fn(item, data) {
    let str = '';
    if (item) {
      str = i18n.tt("自动同步");
    } else {
      str = i18n.tt("手动同步");
    }
    return str;
  }
}, {
  dataIndex: 'opName',
  title: i18n.tt("操作人 "),
  width: 62,
  sorter: true
}, {
  dataIndex: 'brandName',
  title: i18n.tt("品牌名称 "),
  width: 100,
  sorter: true
}, {
  dataIndex: 'storeName',
  title: i18n.tt("门店名称 "),
  width: 100,
  sorter: true
}, {
  dataIndex: 'storeId',
  title: i18n.tt("门店ID "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'provinceName',
  title: i18n.tt("省份 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'cityName',
  title: i18n.tt("城市 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'districtName',
  title: i18n.tt("行政区域 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'address',
  title: i18n.tt("门店地址 "),
  width: 180,
  sorter: true
}];
export default {
  name: 'modalSynRecord',
  components: {
    Modal,
    cdDate,
    Search,
    RsRemark,
    batchManage
  },
  mixins: [eventBus],
  props: {
    name: '',
    reData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: true,
      columns: columns //表格列的配置描述
      ,
      isBatchEdit: false,
      batchEdit: {
        isSureEdit: false,
        cb: function () {}
      },
      startTime: Com.dateFormat(new Date(), 'yyyy-MM-dd'),
      endTime: Com.dateFormat(new Date(), 'yyyy-MM-dd'),
      rData: {
        startTime: Com.dateFormat(new Date(), 'yyyy-MM-dd') + ' 00:00:00',
        //时间开始 
        endTime: Com.dateFormat(new Date(), 'yyyy-MM-dd') + ' 23:59:59' //时间结束
      },
      status: [{
        value: 0,
        label: i18n.tt("全部")
      }, {
        value: 1,
        label: i18n.tt("成功")
      }, {
        value: 2,
        label: i18n.tt("失败")
      }, {
        value: 3,
        label: i18n.tt("同步中")
      }] //同步结果
      ,
      type: [] //同步操作
      ,
      allFail: true //同步结果为失败 且 未处理 返回true
      ,
      selectedId: [] //已经勾选的id集合
      ,
      showDate: false // 用于id相同是无法设置开始时间和结束时间限制，每次关闭弹窗时移除对应的元素
      ,

      filterSyncType: [2, 6, 7, 8, 18, 30, 31, 32, 33, 34, 35, 36, 37, 49, 50, 51]
    };
  },
  created() {
    this.showDate = true;
  },
  mounted() {},
  methods: {
    $requestDataAdapterFn(data) {
      console.log(this.reData);
      let {
        syncThreeId,
        brandId,
        storeId,
        channel,
        syncType
      } = this.reData;
      return {
        syncThreeId,
        brandId,
        storeId,
        channel,
        syncType,
        ...data
      };
    },
    $resetClick() {
      this.startTime = this.rData.startTime.substring(0, 10);
      this.endTime = this.rData.endTime.substring(0, 10);
    },
    $close() {
      //关闭同步记录弹窗
      this.showDate = false;
      this.visible = false;
      setTimeout(() => {
        this.$emit('close', 'synRecord');
      });
    },
    $dataChange(e) {
      this.startTime = e.startTime;
      this.endTime = e.endTime;

      // 加上会筛选没数据，做好记住加上
      if (e.startTime == '') {
        this.rData.startTime = '';
      } else {
        this.rData.startTime = e.startTime + ' 00:00:00';
      }
      if (e.endTime == '') {
        this.rData.endTime = '';
      } else {
        this.rData.endTime = e.endTime + ' 23:59:59';
      }
    },
    $syn(props) {
      //重新同步按钮
      Com.vm.$service.get('reSyncTask', {
        id: props.record.id
      }).then(result => {
        if (result.$.checkResult(result, true)) {
          // this.$bus.$emit('synOperation', true);  //smile那边会用到
          Com.$messager(i18n.tt("重新同步成功"));
          setTimeout(() => {
            this.$emit('save', 'synRecord');
          });
          this.$refs.search.$refresh();
          setTimeout(() => {
            if (this.visible) {
              this.$refs.search.$refresh();
            }
          }, 30000);
        }
      });
    },
    $ignore(props) {
      //忽略按钮
      Modal.confirm({
        content: `<span class=\"mb30 db\">${i18n.tt("忽略同步失败记录则所选失败记录默认已处理，")}</span>${i18n.tt("首页将不再提示异常，确定要忽略失败记录？")}`,
        onOk: () => {
          Com.vm.$service.get('batchIgnoreFail', {
            ids: [props.record.id]
          }).then(result => {
            if (result.$.checkResult(result, true)) {
              // this.$bus.$emit('synOperation', true); //smile那边会用到
              this.$refs.search.$refresh();
            }
          });
        }
      });
    }
  }
}
</script>
<style scoped lang="sass">
            
.box{
  height:544px;
}

</style>
<template>
  <div>
    <cd-modal
      :show="visible"
      :removeModal="true"
      class="recommentCard"
      :title="$lang(`主推卡规则`)"
      @close="$doClose">
      <template slot="modal-body">
        <p class="warn-text">
          <span class="circle">!</span>
          {{ $lang(`注意：1.相同位置、门店、渠道、业务、时间下若有多条规则，则执行创建时间最新的规则内容`) }}
        </p>
        <cd-form ref="ruleForm">
          <cd-form-item prop="ruleName">
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`规则名称`) }}
            </span>
            <cd-input
              v-model="ruleForm.ruleName"
              :placeholder="$lang(`请输入规则名称，最多支持30个字`)"
              :width="230"></cd-input>
          </cd-form-item>
          <cd-form-item prop="locationList">
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`推广位置`) }}
            </span>
            <cd-checkbox-group
              v-model="ruleForm.locationList"
              :min="1">
              <span
                v-for="(location, idx) in locationList"
                :key="idx"
                class="din"
                style="width: 100px">
                <cd-checkbox
                  class="mb5"
                  :label="location.value">
                  {{ location.name }}
                </cd-checkbox>
              </span>
            </cd-checkbox-group>
          </cd-form-item>
          <cd-form-item>
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`推荐卡`) }}
            </span>
            <cd-input
              :listKey="'cid'"
              :isDownBtn="true"
              :width="228"
              :value="cardName"
              type="text"
              @input="$doSearchByKeyWord(arguments)"
              @select="$doSelectByKeyWord(arguments)"
              @blur="$doClearList"
              @focus="$doSearchByKeyWord(arguments)"
              :inputlistdata="cardList"
              :blurClearValue="blurClearValue"
              :placeholder="$lang(`请选择权益卡`)">
              <template
                slot="itemText"
                scope="props">
                {{ props.item.cardName }}
              </template>
            </cd-input>
            <cd-select
              :options="skuList"
              v-model="skuIdList"
              label-key="name"
              :width="120"
              :class="{ selectPlaceholder: !skuIdList }"
              class="ml5 v-top"
              value-key="id"></cd-select>
            <p class="co-tip mt10">{{ $lang(`温馨提示：当权益卡或卡中的规格下架时，则该推荐规则不生效（因为推荐的权益卡不可售）。`) }}</p>
          </cd-form-item>
          <cd-form-item>
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`适用业务类型`) }}
            </span>
            <cd-checkbox-group v-model="ruleForm.validSendTypes">
              <cd-checkbox
                name="check"
                v-for="business in BUSINESS_TYPE"
                :value="business.key"
                :label="business.key">
                {{ business.desc }}
              </cd-checkbox>
            </cd-checkbox-group>
          </cd-form-item>
          <cd-form-item>
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`适用渠道`) }}
            </span>
            <cd-checkbox-group v-model="ruleForm.validFromTypes">
              <cd-checkbox
                name="check"
                class="mb8"
                v-for="channel in CHANNEL_LIST"
                :value="channel.id"
                :label="channel.id">
                {{ channel.name }}
              </cd-checkbox>
            </cd-checkbox-group>
          </cd-form-item>
          <cd-form-item prop="stores">
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`适用门店`) }}
            </span>
            <section>
              <p class="din user-store-title">{{ $lang(`品牌：`) }}</p>
              <div
                class="fb-r fb-v-middle"
                style="display: inline-flex">
                <cd-input
                  :placeholder="$lang(`请选择品牌`)"
                  type="textarea"
                  readonly
                  :width="600"
                  :height="64"
                  :value="brands.map((item) => item.brandName).join(',') || ''"
                  class="mr10"></cd-input>
                <cd-button
                  type="b3"
                  class="btnSelect"
                  @click.native="$openModal('selectBrand')">
                  {{ $lang(`添加品牌`) }}
                </cd-button>
              </div>
            </section>
            <section class="mt20">
              <p class="din user-store-title">{{ $lang(`门店：`) }}</p>
              <cd-radio-group
                v-model="ruleForm.serviceStoreType"
                @change="changeUseStore">
                <div class="mb10">
                  <cd-radio :label="0">{{ $lang(`所有门店可用`) }}</cd-radio>
                </div>
                <div class="mb10">
                  <cd-radio :label="1">{{ $lang(`部分门店可用`) }}</cd-radio>
                  <SelectStoreInput
                    v-if="ruleForm.serviceStoreType == 1"
                    :stores="stores"
                    @update:stores="stores = $event"
                    :required="false"
                    :hasBrandScope="true"
                    :brandId="brands.map((item) => item.brandId)"
                    :placeholder="$lang(`请选择门店，可多选`)"
                    :isSearchStore="false"
                    :isTrueBrand="false"
                    class="ml20 mt5"
                    :width="440"
                    :btnInline="true"></SelectStoreInput>
                </div>
                <div>
                  <cd-radio :label="2">{{ $lang(`部分门店不可用`) }}</cd-radio>
                  <SelectStoreInput
                    v-if="ruleForm.serviceStoreType == 2"
                    :stores="stores"
                    @update:stores="stores = $event"
                    :required="false"
                    :hasBrandScope="true"
                    :brandId="brands.map((item) => item.brandId)"
                    :placeholder="$lang(`请选择门店，可多选`)"
                    :isSearchStore="false"
                    :isTrueBrand="false"
                    class="ml20 mt5"
                    :width="440"
                    :btnInline="true"></SelectStoreInput>
                </div>
              </cd-radio-group>
            </section>
          </cd-form-item>
          <cd-form-item :label="$lang(`可用时间`)">
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`可用时间`) }}
            </span>
            <cd-radio-group v-model="ruleForm.serviceTimeType">
              <div class="mb10">
                <cd-radio :label="0">{{ $lang(`全部时段可用`) }}</cd-radio>
              </div>
              <div>
                <cd-radio :label="1">{{ $lang(`部分时段可用`) }}</cd-radio>
              </div>
            </cd-radio-group>
            <div
              class="time-box mt10"
              v-if="ruleForm.serviceTimeType == 1"
              v-for="(timeSetting, settingIdx) in ruleForm.serviceTimeList">
              <section class="time-header">
                <span>{{ $lang(`时段{0}`, [settingIdx + 1]) }}</span>
                <cd-button
                  v-show="ruleForm.serviceTimeList.length > 1"
                  type="b6"
                  gray
                  @click="clickDelTime(timeSetting, settingIdx)">
                  {{ $lang(`删除`) }}
                </cd-button>
              </section>
              <section class="time-content">
                <section class="time-item">
                  <span class="time-title">{{ $lang(`星期：`) }}</span>
                  <cd-checkbox-group v-model="timeSetting.validWeekDays">
                    <span
                      v-for="(week, idx) in weekList"
                      :key="idx"
                      class="din"
                      style="width: 100px">
                      <cd-checkbox
                        @change="$weekChange(timeSetting, settingIdx, idx, arguments)"
                        class="mb5"
                        :label="week.value">
                        {{ week.name }}
                      </cd-checkbox>
                    </span>
                  </cd-checkbox-group>
                </section>
                <section class="time-item">
                  <span
                    class="time-title"
                    style="margin-top: 12px">
                    {{ $lang(`时间：`) }}
                  </span>
                  <v-add-time
                    v-model="timeSetting.validTimes"
                    @input="changeTime(timeSetting, settingIdx, arguments)"
                    :tip="false"
                    startTimeKey="start"
                    endTimeKey="end"
                    endTimeText
                    startTimeText
                    :deleteBtn="true"
                    :limitTimeLen="7"
                    :showFirstRemoveBtn="true"
                    :onlyAtMaxLenShowRemoveBtn="true"
                    :btnIsIcon="true"></v-add-time>
                </section>
              </section>
            </div>
            <section
              class="time-footer"
              v-if="ruleForm.serviceTimeType == 1">
              <cd-button
                v-show="ruleForm.serviceTimeList.length < 7"
                type="b3"
                @click="clickAddTime">
                {{ $lang(`添加时段`) }}
              </cd-button>
            </section>
          </cd-form-item>
          <cd-form-item prop="status">
            <span slot="label">
              <span class="co-err">*</span>
              {{ $lang(`状态`) }}
            </span>
            <cd-switch
              :checked="status"
              :onText="$lang(`停用`)"
              :offText="$lang(`启用`)"
              style="position: relative"
              name="check"
              :onlyActiveShow="true"
              :btnWidth="44"
              :width="62"></cd-switch>
          </cd-form-item>
        </cd-form>
      </template>
      <div
        slot="modal-footer"
        class="modal-footer">
        <cd-button
          type="b5"
          @click="$doSave">
          {{ $lang(`保存`) }}
        </cd-button>
        <cd-button
          type="b5"
          gray
          @click="$doClose">
          {{ $lang(`放弃`) }}
        </cd-button>
      </div>
    </cd-modal>
    <div v-if="modalConfig.show">
      <select-brands
        v-if="modalConfig.selectBrand.show"
        @close="$closeModal('selectBrand')"
        :required="true"
        :selectedBrand="brands"
        @callback="saveBrand"></select-brands>
    </div>
  </div>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import { SelectStoreInput } from "project/index/components/SelectStore";
import { SelectBrands } from "project/index/components/SelectBrand";
import { BUSINESS_TYPE } from "project/index/enumerations/channel.js";
import { checkTimeFormat } from "project/index/utility.js";
import cdSwitch from "project/index/components/Switch";
import AddTime from "project/index/components/AddTime";
const VALIDITY_TYPE = {
  1: i18n.tt("天"),
  2: i18n.tt("个月"),
  3: i18n.tt("年")
};
let defaultTimeSetting = {
  validWeekDays: [],
  validTimes: [{
    start: "",
    end: ""
  }]
};
let searchTimer;
export default {
  name: "modal-setRecommentCard",
  props: {
    ruleId: Number
  },
  components: {
    SelectStoreInput,
    SelectBrands,
    cdSwitch,
    vAddTime: AddTime
  },
  data() {
    return {
      visible: true,
      ruleForm: {
        ruleName: "",
        locationList: [1],
        recommendRuleId: null,
        cardId: null,
        skuIdList: [],
        brandIds: [],
        storeIds: [],
        extraStoreIds: [],
        serviceStoreType: 0,
        validFromTypes: [],
        validSendTypes: [],
        serviceTimeType: 0,
        serviceTimeList: [{
          validWeekDays: [],
          validTimes: [{
            start: "00:00",
            end: "23:59"
          }]
        }],
        status: 1
      },
      locationList: [{
        value: 1,
        name: i18n.tt("购物车")
      }, {
        value: 2,
        name: i18n.tt("结算页")
      }],
      cardName: "",
      skuIdList: null,
      blurClearValue: false,
      cardList: [],
      skuList: [{
        id: 0,
        name: i18n.tt("请先选择权益卡")
      }],
      VALIDITY_TYPE: VALIDITY_TYPE,
      BUSINESS_TYPE: Object.values(BUSINESS_TYPE),
      CHANNEL_LIST: Com.extend([...Com.base.Platform.channelList, ...(Com.base.Platform.channelListOfExternal || []).filter(item => item.isOrder)]),
      weekList: [{
        name: i18n.tt("星期一"),
        value: 1
      }, {
        name: i18n.tt("星期二"),
        value: 2
      }, {
        name: i18n.tt("星期三"),
        value: 3
      }, {
        name: i18n.tt("星期四"),
        value: 4
      }, {
        name: i18n.tt("星期五"),
        value: 5
      }, {
        name: i18n.tt("星期六"),
        value: 6
      }, {
        name: i18n.tt("星期日"),
        value: 7
      }],
      stores: [],
      brands: [],
      status: true,
      // 弹窗配置
      modalConfig: {
        show: false,
        selectBrand: {
          show: false
        }
      }
    };
  },
  created() {},
  mounted() {
    if (this.ruleId) {
      const self = this;
      self.netGetDetail(self.ruleId).then(() => {
        self.getBenefits(self.ruleForm.cardName, {
          benefitCardIds: [self.ruleForm.cardId]
        }).then(cards => {
          self.skuList = self.setSkuList(cards[0]);
          self.skuIdList = self.ruleForm.skuIdList[0];
        });
      });
    }
  },
  methods: {
    $doSearchByKeyWord(param) {
      this.$doClearList();
      let keyWord = param[0],
        tip = i18n.tt("查无该权益卡，请重新输入！");
      if (keyWord === this.cardName) {
        this.blurClearValue = false;
      } else {
        this.toCategoryId = 0;
        this.blurClearValue = true;
      }
      keyWord && (this.keyWord = keyWord);
      this.cardList = [{
        cardName: i18n.tt("搜索中，请稍候.."),
        cid: 0
      }];
      this.cardName = keyWord;
      if (searchTimer) {
        clearTimeout(searchTimer);
      }
      searchTimer = setTimeout(() => {
        this.getBenefits(keyWord).then(data => {
          if (data.length) {
            this.cardList = [].concat(data);
          } else this.cardList = [{
            cardName: tip,
            cid: 0
          }];
        });
      }, 300);
    },
    $doSelectByKeyWord(param) {
      this.blurClearValue = false;
      let item = param[0];
      let name = "";
      if (item.cid) {
        name = item.cardName;
        this.skuList = this.setSkuList(item);
      } else {
        this.cardList = [];
        this.skuList = [{
          id: 0,
          name: i18n.tt("请先选择权益卡")
        }];
      }
      this.$set(this.ruleForm, "cardId", item.cid);
      this.$set(this, "cardName", name);
      this.skuIdList = this.skuList[0].id;
    },
    $doClearList() {
      this.cardList = [];
    },
    setSkuList(card) {
      let skuList = [];
      if (card && card.payAmountDataList && card.payAmountDataList.length) {
        card.payAmountDataList.forEach(c => {
          skuList.push({
            id: c.skuId,
            name: `${c.periodNum}${this.VALIDITY_TYPE[c.periodType]}`
          });
        });
      } else {
        skuList.push({
          id: 0,
          name: i18n.tt("暂无规格")
        });
      }
      return skuList;
    },
    getBenefits(name, otherParams = {}) {
      return this.$service.get("queryBenefitPage", {
        isSimpleField: true,
        cardName: name,
        pageNow: 1,
        pageSize: 999,
        ...otherParams
      }, {
        isHideLoading: true
      }).then(result => {
        if (!result.$.checkResult(result, true)) {
          return;
        }
        return result.data.rows;
      });
    },
    getCardDetail(cardId) {
      return this.$service.get('getBenefitCard').then(result => {
        return result.data;
      });
    },
    netGetDetail(recommendRuleId) {
      return this.$service.get("getBenefitCardRecommendRule", {
        recommendRuleId
      }).then(result => {
        if (!result.$.checkResult(result, true)) {
          return;
        }
        let data = result.data;
        data.cardId = data.cardId || "";
        data.serviceStoreType = data.serviceStoreType || 0;
        data.storeIds = data.storeIds || [];
        data.extraStoreIds = data.extraStoreIds || [];
        data.validFromTypes = data.validFromTypes || [];
        data.validSendTypes = data.validSendTypes || [];

        // 去掉现在渠道列表不存在的已保存项
        data.validFromTypes = data.validFromTypes.filter(id => this.CHANNEL_LIST.some(channelItem => channelItem.id == id));
        for (const key in data) {
          if (Object.hasOwnProperty.call(this.ruleForm, key)) {
            this.ruleForm[key] = data[key];
          }
        }
        this.cardName = data.cardName;
        this.status = data.status == 1;
        setTimeout(() => {
          this.stores = result.data.stores || [];
          this.brands = result.data.brands || [];
        }, 1000);
      });
    },
    clickAddTime() {
      if (this.ruleForm.serviceTimeList.length >= 7) {
        Com.$messager(i18n.tt("最多添加7个时间段"));
        return;
      }
      this.ruleForm.serviceTimeList.push(Com.extend(defaultTimeSetting));
    },
    clickDelTime(item, index) {
      this.ruleForm.serviceTimeList.splice(index, 1);
    },
    $weekChange(timeSetting, settingIdx, weekIdx, args) {
      console.log(args);
      let [check, event, week] = args;
      // 需要对勾选星期下的时间段，做检查是否存在重叠时间
      if (!check) return;

      // 同个权益卡的时间段不能有相同星期
      let exitWeek = this.ruleForm.serviceTimeList.some((timeSettingV, idxV) => {
        if (idxV != settingIdx && timeSettingV.validWeekDays.includes(week)) {
          return true;
        }
        return false;
      });

      // 找到其他时间段的相同星期的时间集合
      const getOtherTimesWithSameWeek = function (targetWeek, targetSetting, targetSettingIdx, serviceTimeList) {
        let targetWeeks = [targetWeek];
        let otherTimesWithSameWeek = [];
        serviceTimeList.map((timeSettingItem, idx) => {
          // 找到其他的包含相同星期的时间段
          if (idx !== settingIdx && timeSettingItem.validWeekDays.some(week => targetWeeks.includes(week))) {
            // 加参数groupIdx timeIdx 然后 去掉没填完的时间项
            otherTimesWithSameWeek.push(...timeSettingItem.validTimes.map((time, tIdx) => {
              return {
                ...time,
                groupIdx: idx,
                timeIdx: tIdx
              };
            }).filter(time => time.start && time.end));
          }
        });
        return otherTimesWithSameWeek;
      };
      const clearCurWeek = function () {
        timeSetting.validWeekDays = timeSetting.validWeekDays.filter(weekV => weekV != week);
      };
      if (exitWeek) {
        clearCurWeek();
        Com.$messager(i18n.tt("该星期已设置"));
        return;
      }
      let otherTimesWithSameWeek = getOtherTimesWithSameWeek(week, timeSetting, settingIdx, this.ruleForm.serviceTimeList);
      if (!otherTimesWithSameWeek.length) return;
      for (let idx = 0; idx < timeSetting.validTimes.length; idx++) {
        const time = timeSetting.validTimes[idx];
        otherTimesWithSameWeek.unshift(time);
        let result = Com.validTimes(otherTimesWithSameWeek, 0, "start", "start", "end", false, false, true);
        if (result === true) {
          clearCurWeek();
          Com.$messager(i18n.tt("当前星期与其他时间段时间有冲突"));
          break;
        } else if (result.isConflict) {
          clearCurWeek();
          Com.$messager(i18n.tt("当前星期与【时段{0}】的【第{1}段】时间有冲突", [result.conflictTime.groupIdx + 1, result.conflictTime.timeIdx + 1]));
          break;
        }
        otherTimesWithSameWeek.shift();
      }
    },
    // index是某时间段下的某个时间位置
    changeTime(timeSetting, settingIdx, args) {
      let [times, isPass, index, type, startTimeKey, endTimeKey] = args;
      let key = type == 1 ? "start" : "end";
      // add-time.vue会检查时间段X[timeSetting]内的各个时间段[times]是否冲突，未处理与其他的时间段[timeSetting]冲突问题。
      const getOtherTimesWithSameWeek = function (targetSetting, targetSettingIdx, serviceTimeList) {
        let targetWeeks = targetSetting.validWeekDays,
          targetIdx = index;
        let otherTimesWithSameWeek = [];
        serviceTimeList.map((timeSettingItem, idx) => {
          // 找到其他的包含相同星期的时间段
          if (idx !== settingIdx && timeSettingItem.validWeekDays.some(week => targetWeeks.includes(week))) {
            // 加参数groupIdx timeIdx 然后 去掉没填完的时间项
            otherTimesWithSameWeek.push(...timeSettingItem.validTimes.map((time, tIdx) => {
              return {
                ...time,
                groupIdx: idx,
                timeIdx: tIdx
              };
            }).filter(time => time.start && time.end));
          }
        });
        return otherTimesWithSameWeek;
      };
      const clearCurTime = function () {
        let activeTime = timeSetting.validTimes[index];
        activeTime[key] = "";
      };
      let otherTimesWithSameWeek = getOtherTimesWithSameWeek(timeSetting, settingIdx, this.ruleForm.serviceTimeList);
      if (!otherTimesWithSameWeek.length) return;
      otherTimesWithSameWeek.unshift(times[index]);
      //
      let result = Com.validTimes(otherTimesWithSameWeek, 0, key, startTimeKey, endTimeKey, false, false, true);
      if (result === true) {
        clearCurTime();
        Com.$messager(i18n.tt("当前时间与其他时间段时间有冲突"));
      } else if (result.isConflict) {
        clearCurTime();
        Com.$messager(i18n.tt("当前时间与【时段{0}】的【第{1}段】时间有冲突", [result.conflictTime.groupIdx + 1, result.conflictTime.timeIdx + 1]));
      }
    },
    saveBrand(brands) {
      this.stores = [];
      this.brands = brands || [];
      this.$closeModal("selectBrand");
    },
    changeUseStore() {
      this.stores = [];
    },
    $openModal(key) {
      this.modalConfig.show = true;
      this.modalConfig[key].show = true;
    },
    $closeModal(key) {
      this.modalConfig.show = false;
      this.modalConfig[key].show = false;
    },
    $doClose() {
      this.visible = false;
      this.$nextTick(() => {
        this.$emit("close", "edit");
      });
    },
    $doSave() {
      let {
        pass,
        tips
      } = this.checkData();
      if (!pass) {
        Com.$messager(tips);
        return;
      }
      // 品牌id处理
      this.ruleForm.brandIds = this.brands.map(b => {
        return b.brandId;
      });
      // 门店id处理
      if (this.ruleForm.serviceStoreType == 1) {
        this.ruleForm.storeIds = this.stores.map(s => {
          return s.storeId;
        });
      } else if (this.ruleForm.serviceStoreType == 2) {
        this.ruleForm.extraStoreIds = this.stores.map(s => {
          return s.storeId;
        });
      }
      // 规格id处理
      this.ruleForm.skuIdList = [this.skuIdList];
      // 状态处理
      this.ruleForm.status = this.status ? 1 : 2;
      let actionName = this.ruleForm.recommendRuleId ? 'updateBenefitCardRecommendRule' : 'addBenefitCardRecommendRule';
      this.$service.get(actionName, this.ruleForm).then(result => {
        if (!result.$.checkResult(result, true)) {
          return;
        }
        Com.$messager(result.msg);
        this.$doClose();
      });
    },
    checkData() {
      let pass = true,
        tips = "";
      Com.validators.checkLen(this.ruleForm.ruleName, 30, errTips => {
        if (!this.ruleForm.ruleName.length) {
          pass = false;
          tips = i18n.tt("请输入规则名称");
        } else if (errTips) {
          pass = false;
          tips = i18n.tt("请填写30个字内的规则名称");
        }
      }, true);
      if (!pass) return {
        pass,
        tips
      };
      if (!this.ruleForm.cardId) {
        pass = false;
        tips = i18n.tt("请选择一张主推的权益卡");
        return {
          pass,
          tips
        };
      }
      if (!this.skuIdList) {
        pass = false;
        tips = i18n.tt("请选择主推的权益卡的规格");
        return {
          pass,
          tips
        };
      }
      if (this.ruleForm.validSendTypes.length == 0) {
        pass = false;
        tips = i18n.tt("请选择适用的业务类型");
        return {
          pass,
          tips
        };
      }
      if (this.ruleForm.validFromTypes.length == 0) {
        pass = false;
        tips = i18n.tt("请选择适用的渠道");
        return {
          pass,
          tips
        };
      }
      if (this.brands.length == 0) {
        pass = false;
        tips = i18n.tt("请选择适用品牌");
        return {
          pass,
          tips
        };
      }
      if (this.ruleForm.serviceStoreType && this.stores.length == 0) {
        pass = false;
        tips = i18n.tt("请选择适用门店");
        return {
          pass,
          tips
        };
      }

      // 判断配送费特殊时间段是否合理
      if (this.ruleForm.serviceTimeType && this.ruleForm.serviceTimeList && this.ruleForm.serviceTimeList.length) {
        for (let m of this.ruleForm.serviceTimeList) {
          if (!m.validWeekDays.length) {
            pass = false;
            tips = i18n.tt("适用时间星期不能为空");
            return {
              pass,
              tips
            };
          }
          let val = this.$timeEmpty(m.validTimes);
          if (!val) {
            pass = false;
            tips = i18n.tt("适用时间时间不能为空");
            return {
              pass,
              tips
            };
          }
          let val2 = this.checkTimesRule(m.validTimes);
          if (!val2) {
            pass = false;
            tips = i18n.tt("适用时间时间输入有误");
            return {
              pass,
              tips
            };
          }
        }
      }
      return {
        pass,
        tips
      };
    },
    checkTimesRule(times) {
      let ok = true;
      for (let i = -1, st; st = times[++i];) {
        if (!checkTimeFormat(st.start) || !checkTimeFormat(st.end)) {
          ok = false;
          break;
        }
      }
      return ok;
    },
    $timeEmpty(times, sKey = "start", eKey = "end") {
      //判断营业时间是否有空值
      let ok = true;
      for (let i = -1, st; st = times[++i];) {
        if (!st[sKey] || !st[eKey]) {
          ok = false;
          break;
        }
      }
      return ok;
    }
  }
}
</script>
<style scoped lang="scss" scoped="true">
            
.warn-text {
  .circle {
    border: 1px solid #F86D38;
    border-radius: 50%;
    display: inline-block;
    margin-right: 10px;
    width: 14px;
    height: 14px;
    text-align: center;
  }
  padding: 10px;
  background: #F86D3811;;
  border-radius: 4px 4px 4px 4px;
  color: #F86D38;
  margin-bottom: 20px;
}
.selectCard {
  width: 230px;
}
.user-store-title {
  vertical-align: top;
}
.time-box {
  border: 1px solid #eeeeee;
  border-radius: 4px;
  margin-bottom: 10px;
  margin-left: 20px;
  .time-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    background: #f9f9f9;
    height: 40px;
    border-bottom: 1px solid #eeeeee;
  }
  .time-content {
    padding: 20px;
    .time-item {
      display: flex;
      align-items: flex-start;
      .time-title {
      }
    }
  }
}

.time-footer {
  margin-top: 10px;
}
.mt10Improtant {
  margin-top: 10px!important;
}
.mt15Improtant {
  margin-top: 15px!important;
}
.mb8 {
  margin-bottom: 8px;
}

</style>

<style >
            
.recommentCard
  .selectPlaceholder.cd-select-wrapper
  .select-top
  .left-wrapper
  input {
  color: #aaa;
}

</style>

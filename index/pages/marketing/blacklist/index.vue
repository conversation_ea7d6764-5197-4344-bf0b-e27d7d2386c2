<template>
  <cd-page
    class="self-employed"
    type="tab">
    <cd-tabs
      :headers="tabs"
      :active="active"
      @change="$onTabChange">
      <cd-tab
        v-for="tab in tabs"
        :header="tab.tabname"
        :key="tab.code"
        :isShow="showTab(tab)"></cd-tab>
    </cd-tabs>
    <keep-alive><component v-bind:is="tabView"></component></keep-alive>
  </cd-page>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import cdPage from 'project/index/components/Page';
import afterSaleList from './afterSale/list.vue';
import { eventBus } from 'build/plugs/utils';
export default {
  mixins: [eventBus],
  name: 'marketing-blacklist',
  props: {},
  data() {
    return {
      active: 0,
      tabView: 'afterSaleList',
      tabs: [{
        tabname: i18n.tt("售后黑名单"),
        component: 'afterSaleList',
        code: '000300080001'
      }]
    };
  },
  created() {
    let that = this;
    this.init();
  },
  methods: {
    init() {
      let initActiveIdx = this.tabs.findIndex(tab => {
        let rule = Com.$confirmFunctionBtn(tab.code);
        return rule.hasRead && rule.hasRule;
      });
      if (initActiveIdx < 0) {
        Com.$messager(i18n.tt("暂无权限"));
        this.tabView = '';
      } else {
        this.$onTabChange(initActiveIdx);
      }
    },
    $onTabChange(index) {
      let oldActive = this.active;
      this.active = index;
      let rule = Com.$confirmFunctionBtn(this.tabs[index].code);
      if (rule && !rule.hasRule) {
        this.$nextTick(() => {
          Com.showRuleTip();
          this.active = oldActive;
        });
        return;
      }
      this.tabView = this.tabs[index].component;
    }
  },
  computed: {
    showTab() {
      return function (tab) {
        let rule = Com.$confirmFunctionBtn(tab.code);
        if (rule && rule.hasRead) {
          return true;
        }
        return false;
      };
    }
  },
  components: {
    cdPage,
    afterSaleList
  }
}
</script>
<style scoped lang="sass" scoped="true">
            


</style>

<style >
            


</style>

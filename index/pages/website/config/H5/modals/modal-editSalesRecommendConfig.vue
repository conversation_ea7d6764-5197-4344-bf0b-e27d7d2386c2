<template>
    <cd-modal
        :show="visible"
        @close="$doClose"
        :title="modalTitle"
        :btnOkDisabled="btnOkDisabled"
        @callback="$doSave">
        <div slot="modal-body" class="modal-body-component">
            <cd-form class="recommend-edit-form">
                <cd-form-item inline :label="$lang('配置名称')" show-required-symbol>
                    <cd-input v-model="formData.configName" :width="230" :maxlength="100" />
                    <span slot="tip">{{ configNameTips }}</span>
                </cd-form-item>
                <cd-form-item inline :label="$lang('类型名称')" show-required-symbol>
                    <cd-input v-model="formData.name" :width="230" :maxlength="100" />
                    <span slot="tip">{{ nameTips }}</span>
                </cd-form-item>
                <cd-form-item :label="$lang('类型名称(英)')">
                    <cd-input v-model="formData.nameEn" :width="230" :maxlength="200" />
                    <span slot="tip">{{ nameTips }}</span>
                </cd-form-item>
                <cd-form-item :label="$lang('适用客户端')" show-required-symbol>
                    <cd-radio-group v-model="formData.clientType">
                        <cd-radio
                            v-for="item in clientTypes"
                            :key="item.title"
                            :label="item.value">
                        {{item.title}}
                        </cd-radio>
                    </cd-radio-group>
                </cd-form-item>
                <cd-form-item :label="$lang('业务类型统计范围')" show-required-symbol>
                    <cd-radio-group v-model="formData.businessType">
                        <cd-radio
                            v-for="item in discountRangeTypes"
                            :key="item.title"
                            :label="item.value">
                        {{item.title}}
                        </cd-radio>
                    </cd-radio-group>
                </cd-form-item>
                <cd-form-item :label="$lang('展示数量')" show-required-symbol>
                    <cd-select
                        :options="displayNumOptions"
                        :width="230"
                        @change="changeDisplayNum"
                        v-model.number="formData.count"
                        labelKey="title"
                        valueKey="value"></cd-select>
                    <span class="tip" slot="tip">{{ $lang('SOK客户端最大展示数量为{0}，配置超过{0}时客戶端仅展示前{0}个餐品', [2]) }}</span>
                </cd-form-item>
                <cd-form-item :label="$lang('指定门店')">
                    <store-selector
                        :value="selectedStores"
                        :select-required="false"
                        :brandIds="brandIds"
                        @change="changeStoreSelect" />
                </cd-form-item>
            </cd-form>
        </div>
    </cd-modal>
</template>

<script>
import { i18n } from "project/index/libs/i18n";
import { HTMLUtils } from "project/index/utils/html.utils"
import chooseProductComponent from "./chunk-chooseProductComponent.vue"
import storeSelector from "project/index/extras-components/stores/store-selector-with-import-mode.vue";
import { getStoreByIdsPerf } from "project/index/funcs/store.perf";
import { containsSpecialChars } from "./form-valid";

export default {
    components: {
        chooseProductComponent,
        storeSelector
    },
    props: {
        brandInfo: Object,
        parentStatus: {
            type: Number,
            default: 2
        },
        item: {
            type: Object,
            default() {
                return {};
            }
        }
    },
    data() {
        return {
            visible: false,
            formData: {
                configName: "",
                name: "銷量推薦",
                nameEn: "Hot & Selected For You",
                clientType: 9,
                businessType: 3,
                productTypes: [1],
                count: 2,
                storeIds: [],
                status: this.parentStatus || 2,
            },
            selectedProducts: [],
            selectedStores: [],
            displayNumOptions: Array(10).fill(0).map((itm, index) => {
                const val = index + 1
                return {
                    title: val.toString(),
                    value: val
                }
            }),
            clientTypes: [
                {
                    title: "SOK",
                    value: 9
                }
            ],
            discountRangeTypes: [
                {
                    title: i18n.tt("堂食"),
                    value: 3
                }
            ],
            productTypes: [
                {
                    name: i18n.tt("套餐"),
                    value: 1
                }
            ]
        }
    },
    created() {
        this.visible = true;
    },
    computed: {
        isAdd() {
            return !Boolean(this.item) || !Boolean(this.item.rid);
        },
        btnOkDisabled() {
            return !this.formData.name
                || !this.formData.configName
        },
        brandIds() {
            return this.brandInfo && this.brandInfo.brandId ? [this.brandInfo.brandId] : [];
        },
        modalTitle() {
            return this.item ? i18n.tt("编辑配置") : i18n.tt("新增配置")
        },
        configNameTips() {
            return i18n.tt("最多输入{0}个字符", [100])
        },
        nameTips() {
            // 不支持\"^#!{}特殊字符；类型名称将会显示在SOK前端主页
            return `${i18n.tt("最多输入{0}个字符", [100])}, ${i18n.tt("不支持{0}特殊字符", ["\\\"^#!{}"])}; ${i18n.tt("类型名称将会显示在SOK前端主页")}`
        }
    },
    watch: {
        item: {
            deep: true,
            immediate: true,
            handler(_) {
                this.loadConfig()
            }
        }
    },
    methods: {
        displayData(data = {}) {
            this.formData.configName = data.configName
            this.formData.name = data.name
            this.formData.nameEn = data.nameEn
            this.formData.clientType = data.clientType ? data.clientType[0] : 9
            this.formData.businessType = data.businessTypes ? data.businessTypes[0] : 3
            this.formData.productTypes = data.productTypes ? data.productTypes : []
            this.formData.count = data.count || 3
            this.formData.status = data.status || this.item.status || 2
            this.formData.storeIds = data.storeIds || []
        },
        async loadConfig() {
            if (!this.item) return;
            const res = await this.$service.get("getRecommendConfig", { rid: this.item.rid })
            if (!res || !res.data) {
                return;
            }
            this.displayData(res.data)
            const tasks = []
            if (Array.isArray(this.item.storeIds) && this.item.storeIds.length > 0) {
                tasks.push(this.loadStores(this.item.storeIds))
            }
            await Promise.all(tasks)
        },
        async loadStores(storeIds) {
            // 加载门店信息
            const stores = await getStoreByIdsPerf(storeIds)
            this.selectedStores = [].concat(stores)
        },
        changeDisplayNum(val) {
            console.log("显示数量变化：", val)
        },
        changeStoreSelect(stores) {
            this.selectedStores.length = 0
            this.selectedStores.push(...stores)
            this.formData.storeIds = stores.map(item => item.storeId)
        },
        $doClose() {
            this.visible = false;
            this.$nextTick(() => this.$emit('close'));
        },
        $doSave() {
            if (containsSpecialChars([
                this.formData.configName,
                this.formData.name,
                this.formData.nameEn,
            ])) {
                Com.$messager(i18n.tt('配置名称或分类名称包含特殊字符'))
                return;
            }
            Modal.confirm({
                title: i18n.tt("提示"),
                content: HTMLUtils.textCenteredDiv([i18n.tt("保存后即时生效"), i18n.tt("确认保存？")]).html,
                onOk: async () => {
                    // TODO: 保存数据到后端
                    const saveData = {
                        ...this.item,
                        ...this.brandInfo,
                        ...this.formData,
                        type: 3,
                        clientType: undefined,
                        businessType: undefined,
                        clientTypes: Array.isArray(this.formData.clientType) ? this.formData.clientType : [this.formData.clientType],
                        businessTypes: Array.isArray(this.formData.businessType) ? this.formData.businessType : [this.formData.businessType],
                    }
                    if (!this.isAdd) {
                        saveData.rid = this.item.rid
                    }
                    console.log("要保存的数据：", this.isAdd, this.item, saveData)
                    const result = await this.$service.get(this.isAdd ? "addRecommendConfig" : "updateRecommendConfig", saveData)
                    Com.$messager(result.msg)
                    if (!result.$.checkResult(result, true)) {
                        return;
                    }
                    this.visible = false;
                    this.$nextTick(() => this.$emit('save'));
                }
            });
        }
    }
}
</script>

<style lang="scss" scoped>
.modal-body-component {
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
    .header-text {
        color: #FF0000;
    }
    .tab-relative-wrapper {
        position: relative;
        height: 45px;
    }
    .tab-content-wrapper {
        flex: 1;
    }

    .recommend-edit-form {
        padding-bottom: 10px;
    }
}
</style>

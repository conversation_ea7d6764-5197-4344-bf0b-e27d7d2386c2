<template>
  <page
    id="self-status-page"
    hasTip>
    <span slot="page-tip">
      <span class="co-tip">{{ $lang(`当前品牌 : {0}`, [rData.brandName || ""]) }}</span>
    </span>
    <div
      ref="search"
      class="search-contain">
      <div
        class="discontinue-header"
        ref="listHeader">
        <div class="mb20">
          <batch-manage
            :isBatchs="isBatchs"
            @change="batchChange"
            :btnArr="batchs"
            :hasRule="codeConfig.batchManage.hasRule"
            v-if="codeConfig.batchManage.hasRead"></batch-manage>
        </div>
        <cd-form
          ref="ruleForm"
          class="rule-form"
          inline
          id="search-main">
          <cd-form-item :label="$lang(`共用加料名称`)">
            <cd-input
              v-model="rData.name"
              type="text"></cd-input>
          </cd-form-item>
          <cd-form-item :label="$lang(`共用加料ID`)">
            <cd-input-number
              v-model.number="rData.uid"
              type="text"></cd-input-number>
          </cd-form-item>
          <cd-form-item :label="$lang(`共用加料编码`)">
            <cd-input
              v-model="rData.mappingCode"
              type="text"></cd-input>
          </cd-form-item>
          <cd-button
            class="ml10 search-main-btn"
            type="b6"
            @click="getDataList">
            {{ $lang(`查询`) }}
          </cd-button>
          <cd-button
            class="ml10 search-main-btn"
            type="b6"
            @click="$reset"
            gray>
            {{ $lang(`重置`) }}
          </cd-button>
        </cd-form>
      </div>
      <div
        class="shelfStatus-table-box"
        ref="tableContent">
        <transition name="fade"><loading v-show="loading"></loading></transition>
        <div
          v-show="shadowBox"
          class="shadowBox"
          :style="{ height: shadowBoxHeight - 12 + 'px' }"></div>
        <div
          ref="table"
          class="table-dom"
          @scroll="$onScroll">
          <table
            width="100%"
            cellpadding="0"
            cellspacing="0"
            style="table-layout: fixed">
            <colgroup>
              <col />
              <col
                v-for="(column, index) in columns"
                :width="column.width"
                :key="index" />
            </colgroup>
            <thead>
              <tr class="self-status-checkbox-tr">
                <th
                  style="width: 40px; z-index: 3"
                  class="self-status-checkbox-box title-3">
                  <cd-checkbox
                    v-if="dataList && dataList.length"
                    @change="changeSelectRowAll"
                    v-model="selectRowAll"
                    class="self-status-checkbox">&nbsp;</cd-checkbox>
                </th>
                <th
                  v-for="(column, index) in columns"
                  :key="index"
                  class="title-3"
                  :class="{ isFixed: column.isFixed, hasShadow: column.hasShadow && shadowBox }"
                  :style="{ width: column.width + 'px', left: column.isFixed && column.left }">
                  <span>{{ column.title }}</span>
                </th>
                <th class="title-3"></th>
              </tr>
            </thead>
            <tbody v-if="dataList.length > 0">
              <tr
                v-for="(record, rowIndex) in dataList"
                :key="rowIndex"
                :class="{ record0: rowIndex % 2 == 0, record1: rowIndex % 2 == 1, overRow: ~checkedRows.findIndex((item) => item[rowkey] === record[rowkey]) }"
                class="self-status-checkbox-tr">
                <td
                  style="width: 40px; z-index: 2"
                  class="self-status-checkbox-box">
                  <cd-checkbox
                    :label="record"
                    v-model="checkedRows"
                    class="self-status-checkbox">
                    <span></span>
                  </cd-checkbox>
                </td>
                <td
                  class="self-status-td"
                  v-for="(column, index) in columns"
                  :key="index"
                  :class="{ isFixed: column.isFixed }"
                  :style="{ width: column.width + 'px', left: column.isFixed && column.left }">
                  <div v-if="column.dataIndex === 'action'">
                    <cd-button
                      v-show="codeConfig.storeShelfStatus.hasRead"
                      type="b3"
                      @click="storeShelfStatus(record)">
                      {{ $lang(`按门店上下架`) }}
                    </cd-button>
                  </div>
                  <div
                    class="ellipsis1"
                    v-else-if="!column._isFormType"
                    v-html="record[column.dataIndex] ? record[column.dataIndex] : '-'"
                    :title="record[column.dataIndex] ? record[column.dataIndex] : '-'"></div>
                  <div
                    v-else
                    class="form-type-discontinue">
                    <span class="setShelfStatus">
                      <cd-button
                        type="b3"
                        class="btnFn"
                        @click="setDiscontinue($event, { ...record, fromType: column.fromType })">
                        {{ $lang(`品牌上下架`) }}
                      </cd-button>
                      <span class="textInfo">{{ $lang(`品牌上下架`) }}</span>
                    </span>
                  </div>
                </td>
                <td></td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-show="pagination && pagination.total > 0">
          <pagination
            ref="pager"
            :total="pagination.total"
            :currPage="pagination.currPage"
            :pageSize="pagination.pageSize"
            @changepage="$changePage"
            @changesize="$changeSize"
            @refresh="getDataList"></pagination>
        </div>
      </div>
    </div>
    <div
      @click="closeWinBox($event)"
      class="win-box"
      v-if="setDiscontinueData.show">
      <div
        class="popover-box ml5"
        :style="{ top: setDiscontinueData.style.top, left: setDiscontinueData.style.left }">
        <div
          class="popover"
          style="width: 360px; height: 265px"
          :class="setDiscontinueData.arrow">
          <div
            class="fb-c fb-rb"
            style="height: 100%">
            <div v-show="setDiscontinueData.popoverShow">
              <p class="mt30 mb5 fs-sm">{{ $lang(`品牌{0}会将:`, [!setDiscontinueData.isDiscontinue ? $lang("上架") : $lang("下架")]) }}</p>
              <p>{{ $lang(`1、品牌下按门店/餐品上下架的设置全部覆盖；`) }}</p>
              <p v-if="setDiscontinueData.isDiscontinue">{{ $lang(`2、若餐品不满足最小可售卖组合，则关联餐品下架。`) }}</p>
              <p class="fs-md mt20">{{ $lang(`是否确认操作？`) }}</p>
            </div>
            <cd-form v-show="!setDiscontinueData.popoverShow">
              <cd-form-item>
                <cd-radio-group
                  v-model="setDiscontinueData.isDiscontinue"
                  class="discontinue-radios"
                  :line="true">
                  <div class="item mb10">
                    <cd-radio
                      :label="false"
                      style="display: inline-block">
                      <span class="pr30">{{ $lang(`上架`) }}</span>
                      <cd-switch
                        class="switch-item"
                        @change="switchChange('upAppointment', arguments)"
                        :checked="setDiscontinueData.upAppointment"
                        :onText="$lang(`即时`)"
                        :offText="$lang(`预约`)"
                        :width="100"></cd-switch>
                    </cd-radio>
                    <div class="input-box mt10">
                      <input
                        type="text"
                        @blur="$handleDateBlur($event, 'upTime')"
                        :placeholder="$lang(`预约上架时间`)"
                        v-model="setDiscontinueData.upTime"
                        class="date-input w230 ml76"
                        @click="$createDatePicker($event, {dateFmt:'yyyy-MM-dd HH:mm',minDate:'%y-%M-%d %H:%m',isShowClear:false,readOnly:true})"
                        v-show="!setDiscontinueData.isDiscontinue && setDiscontinueData.upAppointment"
                        id="selfTimeDateUp" />
                    </div>
                  </div>
                  <div class="item mb10">
                    <cd-radio
                      :label="true"
                      style="display: inline-block">
                      <span class="pr30">{{ $lang(`下架`) }}</span>
                      <cd-switch
                        class="switch-item"
                        @change="switchChange('downAppointment', arguments)"
                        :checked="setDiscontinueData.downAppointment"
                        :onText="$lang(`即时`)"
                        :offText="$lang(`预约`)"
                        :width="100"></cd-switch>
                    </cd-radio>
                    <div class="input-box">
                      <input
                        type="text"
                        @blur="$handleDateBlur($event, 'downTime')"
                        :placeholder="$lang(`预约下架时间`)"
                        v-model="setDiscontinueData.downTime"
                        class="date-input w230 ml76 mt10"
                        @click="$createDatePicker($event, {dateFmt:'yyyy-MM-dd HH:mm',minDate:'%y-%M-%d %H:%m',isShowClear:false,readOnly:true})"
                        v-show="setDiscontinueData.isDiscontinue && setDiscontinueData.downAppointment"
                        id="selfTimeDateDown" />
                      <br />
                      <cd-input
                        v-model="setDiscontinueData.reason"
                        class="w230 ml76 mt10"
                        :width="230"
                        v-show="setDiscontinueData.isDiscontinue"
                        :placeholder="$lang(`下架原因(10字以内)`)"></cd-input>
                      <span
                        v-if="!validReason"
                        class="error-text"
                        style="left: 76px">
                        {{ $lang(`不能超过10字`) }}
                      </span>
                    </div>
                  </div>
                </cd-radio-group>
              </cd-form-item>
            </cd-form>
            <div class="btn mt30 fb-r fb-center">
              <cd-button
                type="b5"
                class="mr20"
                @click="saveSetDiscontinue">
                {{ $lang(`确认`) }}
              </cd-button>
              <cd-button
                type="b5"
                gray
                @click="initialize">
                {{ $lang(`放弃`) }}
              </cd-button>
            </div>
          </div>
          <span class="popover-arrow"></span>
        </div>
      </div>
    </div>
    <div v-if="showModal">
      <discontinue-publicChargingBatch
        v-if="modalConfig.publicChargingBatch"
        :url="'updateBrandCommonItemDiscontinue'"
        :modalData="modalConfig.publicChargingBatchData"
        @callback="getDataList"
        @cancel="$closeModal"></discontinue-publicChargingBatch>
    </div>
  </page>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
/**
 * 自定义报表
 */
import { eventBus } from "build/plugs/utils";
import Page from "project/index/components/Page";
import cdSearch from "project/index/components/Search";
import batchManage from 'project/index/components/batchManage';
import productPosList from './modal-productPosList';
import Loading from "project/index/components/Loading"; // loading
import { Pagination } from "project/index/components/Table"; //分页
import * as Com from "../../../../common";
import cdSwitch from 'project/index/components/Switch';
import discontinuePublicChargingBatch from "./modal/set-discontinue-publicChargingBatch";
const setDiscontinueData = {
  show: false,
  popoverShow: false,
  style: {
    top: 12 + 'px',
    left: 12 + 'px'
  },
  arrow: 'popover-bottom',
  data: {},
  isDiscontinue: false,
  reason: '',
  downTime: '',
  upTime: '',
  upAppointment: false,
  // 上架 ： 预约 true / 即时 false
  downAppointment: false // 下架 ： 预约 true / 即时 false
};
export default {
  name: "publicChargingShelfStatus",
  mixins: [eventBus],
  props: {},
  data() {
    return {
      setDiscontinueData,
      organizationList: Com.user.data.organizationList.every(v => v.type === 1 || v.type === 2),
      // todo - isle ID1017431 - 权限： 品牌上下架的编辑按钮仅运营商或品牌下可显示和操作，区域账号和门店账号不显示

      shadowBoxHeight: 0,
      shadowBox: false,
      showModal: false,
      modalConfig: {
        posList: false,
        publicChargingBatch: false,
        publicChargingBatchData: {}
      },
      posListConfig: {
        order: {},
        rData: {}
      },
      mappingList: [],
      dataList: [],
      pagination: {
        total: 0,
        currPage: 1,
        pageSize: 20
      },
      rowkey: 'uid',
      loading: false,
      selectRowAll: false,
      checkedRows: [],
      tableNameConfig: {
        show: false,
        name: '',
        cid: ''
      },
      isEdit: false,
      reportOnGoingShow: false,
      reportConfigVisible: false,
      exportMailboxData: {},
      customReportCid: '',
      preview: {
        // 预览数据表格设置
        isRealData: true,
        show: false,
        data: [],
        columns: [],
        title: i18n.tt("预览（真实数据）")
      },
      rowSelection: {},
      updateCid: '',
      showConfig: false,
      // 控制显示报表配置弹框
      configData: {},
      // 接口返回的报表配置信息

      codeConfig: {
        // 批量管理按钮
        batchManage: {
          code: '0001000300100001',
          name: i18n.tt("批量管理"),
          hasRule: true,
          hasRead: false
        },
        storeShelfStatus: {
          code: '0001000300100002',
          name: i18n.tt("按门店上下架"),
          hasRule: true,
          hasRead: false
        }
      },
      batchs: [{
        name: i18n.tt("批量上架"),
        eventName: 'showReportPage'
      }, {
        name: i18n.tt("批量下架"),
        eventName: 'showExportPage'
      }],
      columnsDefault: [{
        dataIndex: "action",
        title: i18n.tt("操作"),
        width: 150,
        isFixed: true,
        left: '40px'
      }, {
        dataIndex: "name",
        title: i18n.tt("共用加料名称"),
        width: 150,
        isFixed: true,
        left: '190px'
      }, {
        dataIndex: "uid",
        title: i18n.tt("共用加料ID"),
        width: 120,
        isFixed: true,
        left: '340px'
      }, {
        dataIndex: "mappingCode",
        title: i18n.tt("共用加料编码"),
        width: 180,
        isFixed: true,
        left: '460px',
        hasShadow: true
      }],
      rData: {
        brandId: '',
        brandName: '',
        name: '',
        uid: null,
        mappingCode: ''
      }
    };
  },
  computed: {
    validReason() {
      let {
        reason,
        isDiscontinue
      } = this.setDiscontinueData;
      if (isDiscontinue && Com.getStrLength(reason) > 20) {
        return false;
      }
      return true;
    },
    columns() {
      // 获取全渠道 - 生成表格头
      let allFormTypes = [].concat(Com.cloneObjects(Com.base.publicInfo.thirdFromType), Com.cloneObjects(Com.base.publicInfo.platFromType));
      let columns = [...this.columnsDefault];
      for (let formType of allFormTypes) {
        let column = {
          title: formType.name,
          dataIndex: 'formType_' + formType.value,
          width: 200,
          sorter: true,
          _isFormType: true,
          fromType: formType.value
        };
        columns.push(column);
      }
      return columns;
    },
    isBatchs() {
      return !!this.checkedRows.length;
    }
  },
  watch: {
    checkedRows(val) {
      if (val.length === this.dataList.length && this.dataList.length > 0) this.selectRowAll = true;else this.selectRowAll = false;
    }
  },
  created() {
    // 按钮权限
    let codeConfig = this.codeConfig,
      self = this;
    for (let key in codeConfig) {
      if (codeConfig.hasOwnProperty(key)) {
        let {
          hasRule,
          hasRead
        } = Com.$confirmFunctionBtn(codeConfig[key].code);
        codeConfig[key].hasRule = hasRule;
        codeConfig[key].hasRead = hasRead;
      }
    }
    this.setDiscontinueData = Object.assign({}, setDiscontinueData);
    this.rData.brandId = Number(this.$route.query.brand);
    this.rData.brandName = Com.base.currentTab.name.split(' - ')[1];
    this.getDataList();
  },
  mounted() {
    if (!this.$refs.search) {
      return;
    }
    if (!this.$refs.table) {
      return;
    }
    var height = this.$refs.search.clientHeight;
    var tableContentHeight = this.$refs.table;
    var searchHeaderHeight = this.$refs.listHeader.clientHeight;
    tableContentHeight.style.height = height - searchHeaderHeight - 45 + "px";
  },
  methods: {
    $onScroll(e) {
      this.shadowBox = e.target.scrollLeft > 0;
    },
    changeSelectRowAll(val) {
      if (!val) this.checkedRows.splice(0);else this.checkedRows = [...this.dataList];
    },
    closeWinBox(e) {
      if (e.target.className === 'win-box') {
        this.initialize();
      }
    },
    storeShelfStatus(record) {
      if (!this.codeConfig.storeShelfStatus.hasRule) {
        Com.showRuleTip();
        return;
      }
      let name = record.name.length > 5 ? record.name.substr(0, 5) + '...' : record.name;
      console.log('record.name', record.name);
      this.$bus.$emit('add-tabItem', {
        code: 'datainput-products-storeShelfStatus',
        url: 'index/datainput/products/storeShelfStatus' + '?brand=' + this.rData.brandId + '&itemId=' + record.uid + '&itemName=' + record.name,
        name: i18n.tt("门店上下架（共用加料：{0} - 品牌：{1}）", [name, this.rData.brandName])
      });
    },
    initialize() {
      this.setDiscontinueData = Object.assign({}, setDiscontinueData);
    },
    saveSetDiscontinue() {
      if (!this.organizationList) {
        Com.showRuleTip();
        return;
      }
      let {
        data,
        isDiscontinue,
        reason,
        upTime,
        downTime,
        upAppointment,
        downAppointment
      } = this.setDiscontinueData;
      let obj = {
          brandId: this.rData.brandId,
          itemIds: [data.uid],
          fromTypes: [data.fromType],
          channelTypes: [data.fromType],
          isDiscontinue: !!isDiscontinue,
          reason: reason,
          upTime: upTime,
          downTime: downTime
        },
        that = this;
      if (!upAppointment) obj.upTime = '';
      if (!downAppointment) obj.downTime = '';
      if (!isDiscontinue) obj.downTime = '';
      if (isDiscontinue) obj.upTime = '';
      if (!obj.isDiscontinue && obj.upTime === '' && upAppointment) {
        Com.$messager(i18n.tt("请填写上架预约时间"));
        return;
      }
      if (obj.isDiscontinue && obj.downTime === '' && downAppointment) {
        Com.$messager(i18n.tt("请填写下架预约时间"));
        return;
      }
      if (obj.isDiscontinue && !this.validReason) {
        Com.$messager(i18n.tt("下架原因不能超过10字"));
        return;
      }
      if (!this.setDiscontinueData.popoverShow && !this.rData.propertyId) {
        this.setDiscontinueData.popoverShow = true;
        return;
      }
      this.$service.post('updateBrandCommonItemDiscontinue', obj).then(result => {
        this.initialize();
        Modal.alert({
          content: result.status === 1 ? i18n.tt("您已操作成功，系统正在努力同步数据，因数据量原因会存在1-5分钟的同步更新时差，请耐心等待") : result.msg,
          onOk: () => {
            this.getDataList();
          }
        });
      });
    },
    $handleDateBlur(event, type) {
      let date = event.target.value;
      this.setDiscontinueData[type] = date;
    },
    switchChange(name, arg) {
      let switchVal = arg[0];
      this.setDiscontinueData[name] = switchVal;
    },
    setDiscontinue(e, record) {
      let top = e.target.getBoundingClientRect().top + 20 + 'px';
      let left = e.target.getBoundingClientRect().left - 170 + 'px';
      let arrow = 'popover-bottom';
      if (window.innerHeight - e.target.getBoundingClientRect().bottom < 300) {
        top = e.target.getBoundingClientRect().top - 270 + 'px';
        arrow = 'popover-top';
      }
      Object.assign(this.setDiscontinueData, {
        style: {
          top,
          left
        },
        arrow,
        data: record,
        show: true
      });
    },
    // 重置按钮
    $reset() {
      this.rData.name = '';
      this.rData.uid = null;
      this.rData.mappingCode = '';
      this.getDataList();
    },
    // 换页
    $changePage(page) {
      this.pagination.currPage = page;
      this.$nextTick(() => {
        this.getDataList();
      });
    },
    // 改变页码大小
    $changeSize(page, size) {
      this.pagination.currPage = page;
      this.pagination.pageSize = size;
      this.$nextTick(() => {
        this.getDataList();
      });
    },
    getDataList() {
      this.checkedRows.splice(0);
      let data = {
        ...this.rData,
        pageNow: this.pagination.currPage,
        pageSize: this.pagination.pageSize
      };
      this.loading = true;
      this.$service.get("pagingCommonPropertyItem", data).then(result => {
        if (!result.$.checkResult(result, true)) return;
        this.loading = false;
        let dataList = result.data.rows;
        if (dataList.length < 1) {
          this.$set(this, "dataList", []);
          return;
        }
        for (let row of dataList) {
          row.isChecked = false;
          if (row.mappingList && row.mappingList.length > 0) {
            let obj = row.mappingList[0];
            row.mappingCode = obj.posName + ': ' + obj.mappingCode;
          }
          // for (let item of row.items) {
          //   row["formType_" + item.channelType] = Com.extend(item);
          // }
        }
        this.pagination.total = result.data.total;
        this.pagination.pageSize = result.data.pageSize;
        this.pagination.currPage = result.data.page;
        this.$set(this, "dataList", dataList);
        let tableH = this.$refs.table.style.height.slice(0, -2) - 0;
        this.shadowBoxHeight = Math.min(tableH, 50 * this.dataList.length + 30);
      });
    },
    $findMapping(item) {
      //查看更多映射码
      this.mappingList = [];
      let data = {
        mappingId: item.uid,
        brandId: this.rData.brandId,
        mappingType: 8,
        pageNow: 1,
        pageSize: 999
      };
      this.$service.post('listByMappingIdOc', data, {
        isHideLoading: true
      }).then(result => {
        if (!result.$.checkResult(result, true)) return;
        item.showMappingCodePopover = true;
        this.mappingList = result.data.rows.map(m => {
          m.posName = m.posName.trim();
          return m;
        });
      });
    },
    $openModal(modalName) {
      this.showModal = true;
      this.modalConfig[modalName] = true;
    },
    $closeModal(modalName, onok) {
      this.showModal = false;
      this.modalConfig[modalName] = false;
      if (onok) {
        this.$refs.search.$refresh();
      }
    },
    // 批量管理
    batchChange(index) {
      this.modalConfig.publicChargingBatchData = {
        itemIds: this.checkedRows.map(item => item[this.rowkey]),
        brandId: this.rData.brandId,
        isDiscontinue: index === 0 ? false : true
      };
      this.$openModal('publicChargingBatch');
    }
  },
  components: {
    Page,
    cdSearch,
    batchManage,
    productPosList,
    Loading,
    Pagination,
    cdSwitch,
    discontinuePublicChargingBatch
  }
}
</script>
<style scoped lang="scss">
            
$color: #68738A;
$rowBackground1: #f5f5f6;
$rowBackground2: #f9f9f9;
$font-size: 12px;

.date-input {
  width: 110px;
  height: 28px;
  padding: 6px 12px;
  background: #ffffff;
}

.w230 {
  width: 230px;
}

.ml76 {
  margin-left: 76px;
}

.ellipsis1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: keep-all;
}


.win-box {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;

  .popover-box {
    position: absolute;
    top: 0;
    left: 0;
  }
}

.iconfont {
  font-size: 12px;
}

.discontinue-header {
  background: #f5f6f7;
  border-bottom: 1px solid #eeeeee;
  padding: 20px 20px 10px 20px;
  position: relative;

  .search-main-btn {
    position: relative;
    top: 5px;
  }
}

.search-contain {
  height: 100%;
  border: 1px solid #eeeeee;
  position: relative;

  .shelfStatus-table-box {
    color: $color;
    font-size: $font-size;
    position: relative;
    width: 100%;
    height: 100%;
    cursor: default;

    .shadowBox {
      position: absolute;
      width: 640px;
      z-index: 2;
      box-shadow: 6px 0px 6px -4px rgba(0, 0, 0, 0.15);
      transition: box-shadow .3s ease, -webkit-box-shadow .3s ease;
    }

    .table-dom {
      overflow-y: auto;
    }

    table {
      border-collapse: separate;
      overflow-x: auto;
      border: 0;

      th {
        word-break: break-all;
        padding: 0 10px;
        border: 0;
        height: 30px;
      }

      td {
        word-break: break-all;
        padding: 0 10px;
        height: 50px;
        line-height: 18px;
        border: 0;
      }

      thead {
        background: #f9f9f9;

        th {
          font-weight: bold;
          position: sticky;
          top: 0;
          z-index: 2;
          background-color: #f9f9f9;
          left: 0;

          .z-sort {
            display: inline-block;
            position: relative;
            min-height: 9px;
            min-width: 7px;
          }

          &.isFixed {
            z-index: 3;
          }

          &.hasShadow {
            box-shadow: 6px 0px 6px -4px rgba(0, 0, 0, 0.15);
          }
        }

        .overHead {
          background: #F1F8FF;
        }
      }

      tbody {

        tr.record0 {
          background-color: #f5f5f6;
        }

        tr.record1 {
          background-color: $rowBackground2;
        }

        tr.overRow {
          background-color: #F1F8FF;
        }

        td {
          position: sticky;
          left: 0;
          background: white;
          z-index: 1;

          &.isFixed {
            z-index: 2;
          }
        }

        .self-status-td {
          div {
            //overflow: hidden;
            //text-overflow: ellipsis;
            //white-space: nowrap;
            //word-break: keep-all;
          }
        }
      }
    }

  }
}

.setShelfStatus {
  .btnFn {
    display: none;
  }

  .textInfo {
    display: inline-block;
  }

  &:hover {
    .btnFn {
      display: inline-block;
    }

    .textInfo {
      display: none;
    }
  }
}



</style>

<style lang="scss">
            
#self-status-page
.self-status-checkbox-tr
.self-status-checkbox-box
.self-status-checkbox.checkbox {
  min-width: 40px;
}

.win-box {
  .popover-box {
    .radio-label {
      display: inline-flex;
      align-items: center;
    }
  }
}


</style>

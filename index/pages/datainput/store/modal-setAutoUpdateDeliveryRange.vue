<template>
  <cd-modal
    :title="$lang(`自动更新配送范围`)"
    :show="visible"
    :btnOkDisabled="btnOkDisabled"
    @close="doClose"
    @callback="doSave">
    <template slot="modal-body">
      <p class="co-tip mb10">{{ $lang(`餐道系统收到来自外卖平台或配送公司的门店配送范围变更通知后，将根据变更渠道自动更新变更后的最新配送范围；若需启用此功能，请完成以下相关配置；`) }}</p>
      <h2 class="title-1 mb10">{{ $lang(`自动更新配送范围`) }}</h2>
      <div class="mb20">
        <cd-button-group>
          <cd-button
            type="b3"
            :value="$lang(`门店独立设置`)"
            :plain="syncBrand"
            @click="changeMode(false)"></cd-button>
          <cd-button
            type="b3"
            :value="$lang(`与品牌一致`)"
            :plain="!syncBrand"
            @click="changeMode(true)"></cd-button>
        </cd-button-group>
      </div>
      <autoUpdateDeliveryRange
        :editData="editData"
        :readOnly="syncBrand"
        @changeAutoUpdateDeliveryRange="changeAutoUpdateDeliveryRange"
        ref="changeRange"></autoUpdateDeliveryRange>
    </template>
  </cd-modal>
</template>
<script>
  import autoUpdateDeliveryRange from '../brand/modules/cu/autoUpdateDeliveryRange.vue';
export default {
  name: 'modal-setAutoUpdateDeliveryRange',
  props: {
    brandId: [String, Number],
    storeId: [String, Number]
  },
  data() {
    return {
      visible: true,
      storeData: {},
      brandData: {},
      syncBrand: false,
      editData: {},
      postData: {
        storeId: this.storeId,
        autoChangeDeliveryRange: {}
      },
      defaultObj: {
        status: false,
        syncBrand: false,
        posId: -1,
        posName: '',
        addType: -1,
        autoSync: null,
        channelSetting: []
      }
    };
  },
  created() {
    Promise.all([this.getStoreData(), this.getBrandData()]).then(result => {
      if (!result[0].$.checkResult(result[0], true)) {
        return;
      }
      if (!result[1].$.checkResult(result[1], true)) {
        return;
      }
      this.storeData = result[0].data;
      this.brandData = result[1].data;
      this.syncBrand = result[0].data.autoChangeDeliveryRange ? result[0].data.autoChangeDeliveryRange.syncBrand : true;
      let curData = this.syncBrand ? Com.extend(result[1].data) : Com.extend(result[0].data);
      if (!curData.autoChangeDeliveryRange) {
        curData.autoChangeDeliveryRange = Com.extend(this.defaultObj);
      }
      this.editData = Com.extend(curData);
      this.postData.autoChangeDeliveryRange = curData.autoChangeDeliveryRange;
    });
  },
  methods: {
    doClose() {
      this.visible = false;
      setTimeout(() => {
        this.$emit("close", "autoUpdateDeliveryRange");
      });
    },
    doSave() {
      if (this.postData && this.postData.autoChangeDeliveryRange) this.postData.autoChangeDeliveryRange.syncBrand = this.syncBrand;
      Com.vm.$service.get("saveAutoChangeDeliveryRange", this.postData).then(result => {
        if (!result.$.checkResult(result, true)) {
          return;
        }
        this.visible = false;
        setTimeout(() => {
          this.$emit("save", "autoUpdateDeliveryRange");
        });
      });
    },
    changeMode(mode) {
      this.syncBrand = mode;
      const curData = this.syncBrand ? Com.extend(this.brandData) : Com.extend(this.storeData);
      if (!curData.autoChangeDeliveryRange) {
        curData.autoChangeDeliveryRange = Com.extend(this.defaultObj);
      }
      this.editData = Com.extend(curData);
      this.$refs.changeRange.updateData();
    },
    changeAutoUpdateDeliveryRange(setObj) {
      this.postData.autoChangeDeliveryRange = setObj;
    },
    getStoreData() {
      return Com.vm.$service.get('getStoreById', {
        storeId: this.storeId
      }, {
        isHideLoading: false
      });
    },
    getBrandData() {
      return Com.vm.$service.get('getBrandById', {
        brandId: this.brandId
      }, {
        isHideLoading: false
      });
    }
  },
  components: {
    autoUpdateDeliveryRange
  },
  computed: {
    btnOkDisabled() {
      const changeData = this.postData.autoChangeDeliveryRange;
      let changePass = false;
      if (Object.keys(changeData).length && changeData.status) {
        if (!(changeData.channelSetting && (changeData.channelSetting.length == 0 || changeData.channelSetting.length && (changeData.channelSetting.some(c => c.rangeName == "") || changeData.channelSetting.some(b => b.updateStatus == -1))) || changeData.addType == -1 || typeof changeData.autoSync !== "boolean" || changeData.autoSync && changeData.posId == -1)) {
          changePass = true;
        }
      } else {
        changePass = true;
      }
      return !changePass;
    }
  }
}
</script>
<style >
            
</style>

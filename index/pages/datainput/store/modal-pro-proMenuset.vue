<template>
  <div>
    <cd-modal
      :show="visible"
      lg
      :title="$lang(`餐品菜单设置`)"
      @close="$doClose"
      @callback="$doSave">
      <template slot="modal-body">
        <cd-button
          type="b3"
          @click="$addMenu(true)">
          {{ $lang(`添加菜单`) }}
        </cd-button>
        <ul
          class="mt10 new-retail"
          v-if="dataList.length">
          <li
            v-for="(item, idx) in dataList"
            :key="idx"
            class="select-item mb10">
            <div class="pl20 mt15 fs14 menu-color">
              {{ $lang(`菜单的名称:`) }}
              <span class="ml10">{{ item.menuName }}</span>
              <span class="mr10 ml30 v-bottom">{{ $lang(`菜单的分类:`) }}</span>
              <div
                class="din toe v-top"
                v-if="item.productTypes && item.productTypes.length"
                style="width: 800px">
                <span
                  v-for="(m, idx1) in item.productTypes"
                  :key="idx1">
                  <span v-if="m.smalls && m.smalls.length">
                    <span
                      v-for="(n, idx2) in m.smalls"
                      :key="idx2">
                      {{ !idx1 && !idx2 ? "" : "，" }}{{ m.name + "-" + n.name }}
                    </span>
                  </span>
                  <span v-if="!m.smalls">{{ idx1 ? "，" : "" }}{{ m.name }}</span>
                </span>
              </div>
              <span v-else>-</span>
            </div>
            <div class="pl20 pt10 pb10">
              <cd-switch
                :isReverseContent="true"
                :checked="!item.isDiscontinue"
                :onText="$lang(`下架`)"
                :offText="$lang(`上架`)"
                :width="90"
                @change="$onChangeSwitch(item, arguments)"></cd-switch>
              <span
                class="ml10 v-top"
                style="height: 28px; line-height: 28px">
                {{ $lang(`上架时间:`) }}
              </span>
              <input
                type="text"
                @blur="$handleDateBlur($event, item)"
                v-model="item.upTime"
                class="date-input w230 v-top"
                @click="$createDatePicker($event, {dateFmt:'yyyy-MM-dd HH:mm',minDate:'%y-%M-%d %H:%m',isShowClear:false,readOnly:true})"
                id="selfTimeDate" />
              <span class="ml30 menu-menuChannels din mr20 toe">
                {{ $lang(`业务渠道:`) }}
                <i v-html="$returnHtml(item.menuChannels)"></i>
              </span>
              <cd-button
                type="b7"
                :height="30"
                class="v-top"
                @click="$menuDel(item)">
                {{ $lang(`从菜单中移除`) }}
              </cd-button>
              <cd-button
                type="b3"
                class="ml10 v-top"
                @click="$menuEdit(item)">
                {{ $lang(`编辑菜单`) }}
              </cd-button>
            </div>
            <div class="price-menu pl20">
              <span class="din">
                {{ $lang(`单价：`) }}
                <cd-currency></cd-currency>
                <cd-input-number
                  class="ml10"
                  :required="true"
                  v-model="item.price"
                  :min="0"
                  :isFloat="true"></cd-input-number>
              </span>
              <span class="din ml30">
                {{ $lang(`包装费：`) }}
                <cd-currency></cd-currency>
                <cd-input-number
                  class="ml10"
                  :required="true"
                  v-model="item.mealFee"
                  :min="0"
                  :isFloat="true"></cd-input-number>
              </span>
            </div>
          </li>
        </ul>
      </template>
    </cd-modal>
    <div v-if="isModal">
      <select-menu
        :proData="proData"
        :title="$lang('商品渠道设置')"
        v-if="isselectMenu"
        @close="$addMenu(false)"
        @save="$saveMenu"
        :selectCategory="{}"
        :categoryData="storeData"
        :type="2"
        :isSelectOne="false"></select-menu>
    </div>
  </div>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import { eventBus } from 'build/plugs/utils';
import cdSwitch from 'project/index/components/Switch';
import selectMenu from './modal-pro-selectMenu';
export default {
  props: {
    storeData: Object,
    pid: [Number, String],
    proData: Object //商品详情信息
  },
  mixins: [eventBus],
  data() {
    return {
      visible: true,
      dataList: [],
      sendTypes: {
        1: i18n.tt("外卖"),
        2: i18n.tt("自取"),
        3: i18n.tt("堂食"),
        4: i18n.tt("外卖预约"),
        5: i18n.tt("预定")
      },
      isselectMenu: false,
      isModal: false
    };
  },
  created() {
    Com.vm.$service.get('getStorePrdouctIntMenuData', {
      storeId: this.storeData.storeId,
      pid: this.pid
    }).then(result => {
      if (!result.$.checkResult(result, true)) return false;
      this.dataList = result.data.map(x => {
        if (!x.upTime) {
          Vue.set(x, 'upTime', '');
        }
        return x;
      });
    });
  },
  methods: {
    $doClose() {
      this.visible = false;
      setTimeout(() => {
        this.$emit('close', 'proMenuset', false);
      });
    },
    $doSave() {
      let dataList = this.dataList.map(x => {
        let discontinueChannelTypes = x.menuChannels.map(x => x.clientType);
        return {
          menuId: x.menuId,
          isDiscontinue: x.isDiscontinue,
          discontinueChannelTypes: discontinueChannelTypes,
          upTime: x.upTime || '',
          price: x.price,
          mealFee: x.mealFee,
          productTypes: x.productTypes
        };
      });
      Com.vm.$service.get('setStoreProductInMenuData', {
        dataList: dataList,
        storeId: this.storeData.storeId,
        pid: this.pid
      }).then(result => {
        if (!result.$.checkResult(result, true)) return false;
        this.visible = false;
        setTimeout(() => {
          this.$emit('close', 'proMenuset', false);
        });
      });
    },
    $handleDateBlur(event, item) {
      let date = event.target.value;
      item.upTime = date;
    },
    $returnHtml(data) {
      let allFromType = JSON.parse(JSON.stringify(Com.base.publicInfo.allFromType));
      let arr = [];
      data.forEach(item => {
        let name = '';
        allFromType.forEach(x => {
          if (x.value == item.clientType) {
            name = x.name;
          }
        });
        if (name) {
          let sendTypes = [];
          item.sendTypes.forEach(m => {
            if (!!this.sendTypes[m]) {
              sendTypes.push(this.sendTypes[m]);
            }
          });
          let str = sendTypes.join("、");
          if (sendTypes.length) name = name + '(' + str + ')';
          arr.push(name);
        }
      });
      return arr.join("、");
    },
    $onChangeSwitch(item, argum) {
      item.isDiscontinue = !argum[0];
    },
    $addMenu(status) {
      this.isModal = status;
      this.isselectMenu = status;
    },
    $saveMenu(name, data) {
      console.log(data);
      let dataList = [];
      data.forEach(m => {
        let menuDatacate = JSON.parse(JSON.stringify(m.productTypes));
        let lev = menuDatacate[0].bigUid ? 2 : 1; //判断是一级分类还是二级分类
        let productTypes = []; //所选分类
        let list = {};
        if (lev == 2) {
          menuDatacate.forEach(x => {
            if (list[x.bigUid]) {
              list[x.bigUid].smalls.push({
                uid: x.uid,
                name: x.name
              });
            } else {
              let smalls = [{
                uid: x.uid,
                name: x.name
              }];
              Vue.set(list, x.bigUid, {
                smalls: smalls,
                uid: x.bigUid,
                name: x.bigName
              });
            }
          });
          for (let attr in list) {
            productTypes.push(list[attr]);
          }
        } else {
          productTypes = menuDatacate.map(x => {
            return {
              uid: x.uid,
              name: x.name
            };
          });
        }
        dataList.push({
          menuId: m.menuId,
          menuName: m.menuName,
          isDiscontinue: m.isUpper,
          upTime: m.upperTime,
          price: m.price,
          mealFee: m.mealFee,
          productTypes: productTypes,
          menuChannels: m.menuChannels
        });
      });
      let typeList = [];
      //有相同分类覆盖

      dataList.forEach(m => {
        let ok = false;
        this.dataList.forEach(x => {
          if (x.menuId == m.menuId) {
            Com.cloneObjects(x, m);
            ok = true;
          }
        });
        if (!ok) typeList.push(m);
      });
      this.dataList = [...this.dataList, ...typeList];
      console.log(this.dataList, '5445', typeList);
      this.$addMenu(false);
    },
    $menuEdit(menu) {
      this.$doClose();
      let tab = {
        code: 'datainput-menu-cu',
        url: 'index/datainput/menu/edit/' + menu.menuId,
        name: i18n.tt("餐单编辑-") + menu.menuName
      };
      if (Com.base.Platform.merchantType == 2) {
        tab = {
          code: 'datainput-menu-newRetail',
          url: 'index/datainput/menu/newRetail/' + menu.menuId,
          name: i18n.tt("餐单编辑-") + menu.menuName
        };
      }
      let self = this;
      this.$bus.$on('editMenu', cb => {
        cb && cb(self.storeData.brandId, false, 0, self.storeData.storeId);
      });
      this.$bus.$emit('add-tabItem', tab);
    },
    $menuDel(item) {
      let dataList = this.dataList.filter(m => m.menuId != item.menuId).map(x => x);
      Vue.set(this, 'dataList', dataList);
    }
  },
  mounted() {},
  computed: {},
  components: {
    cdSwitch,
    selectMenu
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {});
  }
}
</script>
<style scoped>
            
.date-input{
    height:28px;
    padding-left:10px;
}
 .select-item{
     border:1px solid #eee;
     background:#f5f6f7;
     /*padding:10px 20px;*/
     /*padding:14px 20px 12px 20px;*/
     
 }
 .price-menu{
     background: #f9f9f9;
     border-top:1px solid #eee;
     border-left:1px solid #eee;
     height:60px;
     padding-top:20px;
 }
 .menu-menuChannels{
     width:636px;
     height: 28px;
     line-height: 28px;
     overflow:hidden;
 }

</style>

<style >
            


</style>

<template>
  <span class="select-check">
    <span
      class="select-input"
      @click="$showList"
      :style="styleObject">
      <cd-input
        :class="{ all: true, 'select-disabled': disabled }"
        :disabled="disabled"
        type="text"
        :readonly="true"
        v-model="selectText"
        :width="width"></cd-input>
    </span>
    <div
      id="textId"
      class="formType-list-box monitor-mBorder monitor-mbackground"
      v-show="myisShows"
      @click="handleStopEvent($event)">
      <cd-checkbox-group v-model="myisCheckeds">
        <span
          :class="{ 'check-li': true, 'select-li': myisCheckeds.indexOf(item[valueKey]) > -1 }"
          v-for="item in currentArr"
          :style="{ width: width - 20 + 'px' }">
          <cd-checkbox
            @change="everyCheck"
            class="formType-list pl10"
            name="formTypeText"
            :labelKey="labelKey"
            :valueKey="valueKey"
            :label="item[valueKey]">
            {{ item[labelKey] }}
          </cd-checkbox>
        </span>
      </cd-checkbox-group>
    </div>
  </span>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import { eventBus } from "build/plugs/utils";
var showNum = 0;
export default {
  name: "customizeSelectCheck",
  mixins: [eventBus],
  props: {
    isShows: {
      type: Boolean,
      default: false
    },
    // 修改label文字
    label: {
      type: String,
      default: ""
    },
    // 接口请求过来的数据
    clientTypeList: {
      type: Array,
      default() {
        return [];
      }
    },
    value: {
      type: Array,
      default() {
        return [];
      }
    },
    allText: {
      // 选择下拉框全部的时候输入框显示的文字
      type: [String, Number],
      default: i18n.tt("全部")
    },
    allTextDefault: {
      // 下拉框里面的全部文字
      type: [String, Number],
      default: i18n.tt("全部")
    },
    labelKey: {
      type: String,
      default: "label"
    },
    valueKey: {
      type: String,
      default: "value"
    },
    width: {
      type: Number,
      default: 110
    },
    height: {
      type: Number,
      default: 28
    },
    isAllselect: {
      type: Boolean,
      default: false
    },
    // 当选中多个时显示的文字
    multipleSelectedText: {
      type: String,
      default: i18n.tt("部分平台")
    },
    disabled: {
      type: Boolean,
      default: false
    },
    hideAllSelect: {
      type: Boolean,
      default: false
    },
    canCancelAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      myisShows: this.isShows,
      allComplete: this.allText,
      myisCheckeds: this.value,
      allChecked: true,
      currentVal: this.value,
      currentArr: [],
      selectText: '',
      index: 1
    };
  },
  watch: {
    value: {
      handler(val, oldVal) {
        this.myisCheckeds = val;
        this.selectText = '';
        if (this.myisCheckeds && this.myisCheckeds.length && this.myisCheckeds[0] === 0) {
          this.selectText = i18n.tt("全部");
        } else if (this.myisCheckeds && this.myisCheckeds.length && this.myisCheckeds.indexOf(0) < 0) {
          if (this.clientTypeList.length == this.myisCheckeds.length && this.index < 3) {
            this.myisCheckeds = [0];
            this.selectText = i18n.tt("全部");
          } else {
            this.selectText = i18n.tt("部分类型");
          }
        }
        if (!this.myisCheckeds || !this.myisCheckeds.length) {
          this.selectText = '';
        }
        this.index++;
      },
      immediate: true
    }
  },
  methods: {
    $showList() {
      if (this.disabled) {
        return;
      }
      document.body.removeEventListener("click", this.handleClose);
      this.currentArr = this.clientTypeList.slice(0);
      if (this.valueKey != "value") {
        this.currentArr.unshift({
          [this.labelKey]: this.allTextDefault,
          [this.valueKey]: 0
        });
      } else {
        this.currentArr.unshift({
          label: this.allTextDefault,
          value: 0
        });
      }
      if (this.myisShows) {
        this.myisShows = false;
      } else {
        this.myisShows = true;
      }
      this.$emit("isshowOption", false);
      if (this.myisShows) {
        setTimeout(() => {
          document.body.addEventListener("click", this.handleClose);
        });
      }
      if (this.hideAllSelect && this.currentArr.length) {
        this.currentArr.shift();
      }
    },
    handleClose() {
      this.myisShows = false;
    },
    handleStopEvent($event) {
      $event.stopPropagation();
    },
    //   全选与反选
    allCkeckbox(checked) {
      let myisCheckeds = [];
      this.currentArr.forEach(n => {
        myisCheckeds.push(n.value);
      });
      if (checked) {
        this.myisCheckeds = myisCheckeds;
      } else {
        this.myisCheckeds = [];
      }
    },
    $handChange() {
      this.allCkeckbox();
    },
    // 点击下拉每一项select显示每一项的文字
    everyCheck(item, ev, label, checked) {
      if (!label) {
        if (checked) {
          this.myisCheckeds = [0];
        }
      } else {
        if (this.myisCheckeds.indexOf(0) > -1) {
          if (this.isAllselect) {
            let idx = this.myisCheckeds.indexOf(0);
            this.myisCheckeds.splice(idx, 1);
          } else {
            this.myisCheckeds.shift();
          }
        }
      }
      if (!checked) {
        if (!this.myisCheckeds.length && !this.canCancelAll) {
          this.myisCheckeds = [label];
          setTimeout(() => {
            this.myisCheckeds = [label];
          });
        }
      }
      if (this.myisCheckeds[0] == 0) {
        this.$emit("input", this.myisCheckeds);
        this.$emit("checkselect", {
          value: [],
          currenLabel: label,
          currenChecked: checked
        });
      } else {
        this.$emit("input", this.myisCheckeds);
        this.$emit("checkselect", {
          value: this.myisCheckeds,
          currenLabel: label,
          currenChecked: checked
        });
      }
    }
  },
  computed: {
    styleObject: function () {
      let _style = {};
      if (this.width) {
        _style.width = this.width + "px";
      }
      if (this.height) {
        _style.height = this.height + "px";
      }
      return _style;
    }
  }
}
</script>
<style scoped>
            
.all {
  position: absolute;
  z-index: 99;
  color: #68738A;
}
.all::before {
  position: absolute;
  content: "";
  top: 0px;
  right: 0px;
  width: 19px;
  height: 28px;
  background: #d7d7d7;
  z-index: 2;
}
.all::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 6px;
  margin: auto 0;
  width: 7px;
  height: 0;
  border-top: 6px solid #f1f3f4;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  z-index: 3;
}
.formType-list-box {
  position: absolute;
  top: 28px;
  z-index: 999;
  width: 100%;
  height: 175px;
  overflow-y: auto;
  border-top: none;
}
.formType-list,
.allText {
  padding-top: 7px;
  padding-bottom: 7px;
}
.labelText {
  padding-right: 7px;
  color: #68738A;
}

.select-check {
  position: relative;
  display: inline-block;
}

.select-input {
  display: inline-block;
}
.check-li {
  display: inline-block;
  height: 28px;
  width: 90px;
  overflow: hidden;
}
.check-li:hover {
  background: #F1F8FF;
}
.monitor-mbackground {
  background: #fafafb;
}
.select-disabled {
  cursor: not-allowed;
}
.select-li {
  background: #eee;
}

</style>

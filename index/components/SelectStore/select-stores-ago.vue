<template>
  <div>
    <transfer-table
      ref="compSelStore"
      class="comp-sel-store"
      :modalTitle="modalTitle"
      :selected="selected"
      :leftColumns="leftColumns"
      :configColumns="configColumns"
      :rightColumns="rightColumns"
      :leftWidth="820"
      :leftHeight="655"
      :rightWidth="420"
      :rightHeight="655"
      :rightHeaderHeight="60"
      :rightBtnText="$lang(`清空全部门店`)"
      rowkey="storeId"
      :leftSearchData="lData"
      :openUrl="url"
      @close="$doClose"
      :cbKeyArr="['storeId', 'storeName', 'brandName', 'brandId']"
      :isCheckOne="isCheckOne"
      :leftSearchBtnStyle="{ margin: '2px 8px 2px 0' }"
      :leftResetBtnStyle="{ margin: '2px 8px 2px 0' }"
      :callback="$doSave"
      :maxSelectNum="maxSelectNum"
      :maxSize="maxSize"
      :required="required"
      @resetClick="$resetClick"
      :setResetData="setResetData"
      :slotColumns="['mappingCode']"
      :isChangeField="isChangeField"
      :btnOkText="btnOkText"
      lg
      @modifyColumns="$modifyColumns"
      fillSelectListField
      @get-data="getData"
      isSlotFooter
      @table-checked="handleChecked"
      @clearAllSelected="clearAllSelected">
      <template
        slot="leftHeader"
        class="store-search">
        <cd-input
          v-if="showBrand && filterCondition.includes('brandName')"
          :placeholder="$lang(`品牌名称`)"
          @input="$doSearchByKeyWord('brand', arguments)"
          @select="$doSelectByKeyWord('brand', arguments)"
          @blur="$doClearList"
          :inputlistdata="searchBrandData"
          @focus="$doSearchByKeyWord('brand', arguments)"
          v-model="brandName"
          class="mr8 mb10"
          :listKey="'brandName'">
          <template
            slot="itemText"
            scope="props">
            {{ props.item.brandName }}
          </template>
        </cd-input>
        <cd-input
          v-if="filterCondition.includes('storeName')"
          v-model="lData.storeName"
          :placeholder="$lang(`门店名称`)"
          class="mr8 mb10"></cd-input>
        <cd-input
          v-if="filterCondition.includes('mappingCode')"
          class="mr8"
          :placeholder="$lang(`门店映射码`)"
          v-model="lData.mappingCode"
          type="text"></cd-input>
        <cd-input-number
          v-if="filterCondition.includes('storeId')"
          v-model="lData.storeId"
          :placeholder="$lang(`门店ID`)"
          @input="$input"
          class="mr8 mb10"></cd-input-number>
        <cd-input
          v-if="filterCondition.includes('remarkOne')"
          v-model="lData.remarkOne"
          :placeholder="$lang(`门店标签1`)"
          :maxlength="50"
          class="mr8 mb10"></cd-input>
        <cd-input
          v-if="filterCondition.includes('remarkTwo')"
          v-model="lData.remarkTwo"
          :placeholder="$lang(`门店标签2`)"
          :maxlength="50"
          class="mr8 mb10"></cd-input>
        <cd-input
          v-if="filterCondition.includes('remarkThree')"
          v-model="lData.remarkThree"
          :placeholder="$lang(`门店标签3`)"
          :maxlength="50"
          class="mr8 mb10"></cd-input>
        <cd-select
          v-if="isStatusForSearch"
          :options="statusList"
          v-model="lData.status"
          class="mr8 mb10"></cd-select>
        <select-org-btn
          v-if="filterCondition.includes('oIds')"
          class="mr8 mb10 select-org-btn"
          ref="refOrg"
          @save="saveOrg"
          :parentData="lData"
          :brandIdList="lData.brandIdList"></select-org-btn>
        <cd-region
          :key="regionKey"
          v-if="filterCondition.includes('provinceId')"
          @change="$changeRegion"
          inline
          class="mr8 mb10"
          v-model="region"></cd-region>
      </template>
      <template slot="leftbtn">
        <cd-button
          type="b3"
          class="screen-btn"
          @click="customScreen"
          :height="24"
          v-if="isCustomScreen">
          <img
            src="../Search/icon_custom.png"
            alt="icon" />
          {{ $lang(`自定义条件`) }}
        </cd-button>
      </template>
      <template
        slot="rightHeader"
        scope="data">
        <p>
          <span class="pr10">{{ $lang(`已选择门店`) }}</span>
          {{ $lang(`共`) }}
          <span style="color: #f86d38">{{ data.allSelectedData.length }}</span>
          {{ $lang(`家`) }}
        </p>
      </template>
      <template
        slot="mappingCode"
        scope="props">
        <MappingCodeItem :item="props.record"></MappingCodeItem>
      </template>
      <template
        slot="footer"
        scope="data">
        <div class="transfer-footer">
          <cd-button
            type="b5"
            @click="handleSave"
            :disabled="data.btnOkDisabled">
            {{ $lang(`保存`) }}
          </cd-button>
          <cd-button
            v-if="isSearchResultBtn"
            type="b5"
            :disabled="totalForData < 1 || !!tableSelect.length"
            @click="handleSaveQuery">
            {{ $lang(`按查询结果选择门店`) }}
          </cd-button>
          <cd-button
            type="b5"
            gray
            @click="handleClose">
            {{ $lang(`放弃`) }}
          </cd-button>
        </div>
      </template>
    </transfer-table>
    <cd-custom-item
      v-if="showCustomItem"
      :show-brand="showBrand"
      @onSuccess="onSuccessCustom"
      :customType="2"
      @close="showCustomItem = false"></cd-custom-item>
  </div>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import cdRegion from 'project/index/components/Region';
import TransferTable from 'project/index/components/TransferTable';
import MappingCodeItem from 'project/index/components/MappingCodeItem';
import { SelectOrgBtn } from 'project/index/components/OrganizationTree';
import cdCustomItem from './custom-item';
let searchTimer,
  selectedOrganizationList = [];
const leftColumns = [{
  dataIndex: 'brandName',
  title: i18n.tt("品牌名称 "),
  width: 70,
  sorter: true
}, {
  dataIndex: 'storeName',
  title: i18n.tt("门店名称 "),
  width: 100,
  sorter: true
}, {
  dataIndex: 'storeId',
  title: i18n.tt("门店ID "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'mappingCode',
  title: i18n.tt("门店映射码 "),
  width: 90,
  sorter: true
}, {
  dataIndex: 'countryName',
  title: i18n.tt("国家/地区 "),
  width: 70,
  sorter: true
}, {
  dataIndex: 'provinceName',
  title: i18n.tt("省份 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'cityName',
  title: i18n.tt("城市 "),
  width: 60,
  sorter: true
}, {
  dataIndex: 'districtName',
  title: i18n.tt("行政区域 "),
  width: 70,
  sorter: true
}, {
  dataIndex: 'remarkOne',
  title: i18n.tt("门店标签1 "),
  width: 70,
  sorter: true
}, {
  dataIndex: 'remarkTwo',
  title: i18n.tt("门店标签2 "),
  width: 70,
  sorter: true
}, {
  dataIndex: 'remarkThree',
  title: i18n.tt("门店标签3 "),
  width: 70,
  sorter: true
}];
const rightColumns = [{
  dataIndex: 'brandName',
  title: i18n.tt("品牌名称 "),
  width: 80
}, {
  dataIndex: 'storeName',
  title: i18n.tt("门店名称 "),
  width: 80
}, {
  dataIndex: 'storeId',
  title: i18n.tt("门店ID "),
  width: 60
}, {
  dataIndex: '__action',
  title: '',
  width: 40
}];
export default {
  name: 'CDSelectStore',
  components: {
    cdRegion,
    TransferTable,
    MappingCodeItem,
    SelectOrgBtn,
    cdCustomItem
  },
  props: {
    isSearchResultBtn: {
      type: Boolean,
      default: false
    },
    url: {
      type: String,
      default: 'findStoreList'
    },
    // 品牌id 【如传品牌id，则不显示品牌相关筛选条件】
    brandId: {
      type: [Number, Array],
      default: 0
    },
    // 已选数据
    selected: {
      type: Array,
      default: () => []
    },
    // 是否显示【状态】筛选条件
    // TODO：20220927 功能未完善，UI需确认
    isStatusForSearch: {
      type: Boolean,
      default: false
    },
    // 是否显示自定义查询项
    isCustomScreen: {
      type: Boolean,
      default: true
    },
    // 是否可以全选 false-可 true-不可
    isCheckOne: {
      type: Boolean,
      default: false
    },
    // 最多选择项
    maxSelectNum: {
      type: Number,
      default: -1
    },
    maxSize: {
      // 最多选择项【与maxSelectNum都是限制选择数量，但交互效果不同】
      type: Number,
      default: null
    },
    // 所选数据只能选同一品牌
    singleBrand: {
      type: Boolean,
      default: false
    },
    // 是否必选
    required: {
      type: Boolean,
      default: true
    },
    // 自定义左边表列数据
    isChangeField: {
      type: Boolean,
      default: true
    },
    //若需增加筛选条件，从这传入筛选新增筛选条件
    replenishSearchData: {
      type: Object,
      default() {
        return {};
      }
    },
    //右侧显示字段
    selectColumns: Array,
    modalTitle: {
      type: String,
      default: i18n.tt("选择门店")
    },
    //弹窗确认按钮的文字
    btnOkText: String
  },
  data() {
    return {
      tableSelect: [],
      totalForData: 0,
      regionKey: new Date().valueOf(),
      leftColumns,
      configColumns: [],
      statusList: [{
        value: null,
        label: i18n.tt("全部")
      }, {
        value: 1,
        label: i18n.tt("启用")
      }, {
        value: 2,
        label: i18n.tt("停用")
      }],
      rightColumns: this.selectColumns ? this.selectColumns : rightColumns,
      setResetData: null,
      lData: {
        brandIdList: [],
        storeId: 0,
        storeName: '',
        oIds: [],
        countryId: 0,
        provinceId: 0,
        cityId: 0,
        districtId: 0,
        mappingCode: '',
        remarkOne: '',
        remarkTwo: '',
        remarkThree: ''
      },
      searchData: this.replenishSearchData,
      defaultData: JSON.parse(JSON.stringify(this.replenishSearchData)),
      brandList: [],
      storeList: [],
      brandName: '',
      storeName: '',
      searchBrandData: [],
      searchStoreData: [],
      showCustomItem: false,
      filterCondition: [],
      sid: null
    };
  },
  computed: {
    region() {
      return {
        countryId: this.lData.countryId,
        cityId: this.lData.cityId,
        districtId: this.lData.districtId,
        provinceId: this.lData.provinceId
      };
    },
    showBrand() {
      const {
        brandId
      } = this;
      if (Array.isArray(brandId) && brandId.length) {
        return false;
      } else if (!Array.isArray(brandId) && brandId !== 0) {
        return false;
      }
      return true;
    }
  },
  watch: {
    replenishSearchData: {
      handler: function (val) {
        for (let attr in val) {
          if (Com.$type(this.lData[attr]) === 'undefined') {
            Vue.set(this.lData, attr, val[attr]);
          } else {
            this.lData[attr] = val[attr];
          }
        }
        if (Com.$type(this.lData.storeId) !== 'undefined' && this.lData.storeId === '') {
          this.lData.storeId = null;
        }
      },
      deep: true
    }
  },
  created() {
    this.initField();
    this.initSearchBox();
  },
  mounted() {
    this.tableSelect = [...this.selected];
    const {
      brandId
    } = this;
    if (Array.isArray(brandId) && brandId.length > 0) {
      this.lData.brandIdList = [].concat(brandId);
    } else if (!Array.isArray(brandId) && brandId !== 0) {
      this.lData.brandIdList = [brandId];
    }
    const val = JSON.parse(JSON.stringify(this.replenishSearchData));
    for (let attr in val) {
      if (Com.$type(this.lData[attr]) === 'undefined') {
        Vue.set(this.lData, attr, val[attr]);
      } else {
        this.lData[attr] = val[attr];
      }
    }
    this.setResetData = {
      ...this.lData
    };
    if (Com.$type(this.lData.storeId) !== 'undefined' && this.lData.storeId === '') {
      this.lData.storeId = null;
    }
  },
  methods: {
    saveOrg(list) {
      selectedOrganizationList = list;
    },
    clearAllSelected() {
      this.tableSelect = [];
    },
    handleChecked(data) {
      this.tableSelect = data;
    },
    getData(data) {
      this.totalForData = data.total || 0;
    },
    async handleSaveQuery() {
      let result = await this.$service.get('findStoreList', {
        ...this.lData,
        pageSize: 1
      });
      if (!result.$.checkResult(result, true)) return;
      if ('rows' in result.data && result.data.rows.length < 1) {
        Com.$messager(i18n.tt("暂无查询出可选门店"));
        return;
      }
      if (this.maxSize && result.data.total > this.maxSize) {
        Modal.alert({
          content: i18n.tt("所选门店数量已超过限制，最多导入 {0} 家门店，请重新导入", [this.maxSize])
        });
        return;
      }
      let resultData = result.data.rows[0];
      let dataDefault = {
        ...this.lData
      };
      delete dataDefault.pageSize;
      delete dataDefault.pageNow;
      let data = Object.assign({}, {
        ...dataDefault,
        storesNum: result.data.total
      },
      // 这个是给前端显示用的，随便塞
      {
        dataDefault
      } // 这个值不要动是传给后端的，findStoreList怎么查怎么传
      );
      if (this.showBrand && this.filterCondition.includes('brandName')) {
        // 有选择品牌
        data['brandName'] = this.brandName;
        data['brandId'] = this.lData.brandIdList[0];
      }
      for (const dataKey in this.region) {
        if (this.lData[dataKey]) {
          data[dataKey] = this.region[dataKey];
          if (dataKey === 'countryId') data['countryName'] = resultData.countryName;
          if (dataKey === 'cityId') data['cityName'] = resultData.cityName;
          if (dataKey === 'districtId') data['districtName'] = resultData.districtName;
          if (dataKey === 'provinceId') data['provinceName'] = resultData.provinceName;
        }
      }
      if (this.lData.oIds.length > 0) {
        data['organizationNames'] = selectedOrganizationList.map(({
          name
        }) => name).join(',');
        data['organizations'] = selectedOrganizationList;
      }
      this.handleClose();
      this.$nextTick(() => {
        this.$emit('callback', data, 'selectCatShow');
      });
    },
    $resetLData(data) {
      let cityArr = ['countryId', 'cityId', 'provinceId', 'districtId'],
        hasCity = false;

      // 只显示 不查询
      for (const dataKey in data) {
        if (dataKey in this.lData && this.checkTrim(data[dataKey])) {
          if (this.showBrand && this.filterCondition.includes('brandName') && dataKey === 'brandIdList') {
            this.brandName = data.brandName;
            this.lData['brandIdList'] = [data['brandId']];
          } else if (this.filterCondition.includes('provinceId') && cityArr.includes(dataKey)) {
            this.lData[dataKey] = data[dataKey];
            hasCity = true;
          } else if (this.filterCondition.includes(dataKey)) {
            this.lData[dataKey] = data[dataKey];
          }
        }
      }
      if (hasCity) {
        // 刷新组件
        this.regionKey = new Date().valueOf();
      }
      if (this.$refs.refOrg && this.lData.oIds.length > 0) {
        // 回选组织架构数据
        this.$refs.refOrg.modelSelectedOrganizationList(data.organizations);
      }
    },
    checkTrim(data) {
      if (Array.isArray(data)) return Boolean(Object.keys(data).length);else if (typeof data === 'object' && JSON.stringify(data) === '{}') {
        return false;
      } else {
        return Boolean(data);
      }
    },
    handleClose() {
      this.$refs.compSelStore.$doClose();
    },
    handleSave() {
      this.$refs.compSelStore.$doSave();
    },
    async initField() {
      try {
        const result = await Com.vm.$service.get('getFieldSort', {
          type: 15
        });
        if (!result.$.checkResult(result, true)) {
          return false;
        }
        if (result.data) {
          const {
            sid,
            fieldList
          } = result.data;
          this.sid = sid;
          const allColumns = [...this.leftColumns];
          const filterArr = [];
          const otherArr = [];
          allColumns.forEach(item => {
            if (fieldList.includes(item.dataIndex)) {
              filterArr.push(item);
            } else {
              otherArr.push(item);
            }
          });
          const filterHasMap = new Map(filterArr.map(item => [item.dataIndex, item]));
          const leftColumns = [];
          fieldList.forEach(key => {
            leftColumns.push(filterHasMap.get(key));
          });
          this.leftColumns = leftColumns;
          this.configColumns = otherArr;
        }
      } catch (err) {
        console.error(err);
      }
    },
    async initSearchBox() {
      if (!this.isCustomScreen) {
        this.setDefaultFilterCondition();
        return false;
      }
      try {
        const result = await Com.vm.$service.get('getSearchBoxSort', {
          customFieldType: 2,
          locationType: 6
        });
        if (!result.$.checkResult(result, true)) {
          return false;
        }
        if (result.data && result.data.searchBoxList.length) {
          const queryArr = [];
          result.data.searchBoxList.forEach(item => {
            queryArr.push(item.field);
          });
          this.filterCondition = queryArr;
        } else {
          this.setDefaultFilterCondition();
        }
        this.$nextTick(() => {
          console.log(123);
          this.$refs.compSelStore.$resizeTableHeight();
        });
      } catch (err) {
        console.error(err);
      }
    },
    setDefaultFilterCondition() {
      const arr = ['brandName', 'storeName', 'mappingCode', 'storeId', 'remarkOne', 'remarkTwo', 'remarkThree', 'oIds', 'provinceId'];
      this.filterCondition = arr;
    },
    customScreen() {
      this.showCustomItem = true;
    },
    onSuccessCustom() {
      this.$refs.compSelStore.$reset();
      this.initSearchBox();
    },
    //多门店保存
    $doSave(selected, cb) {
      if (this.singleBrand && selected.length >= 2) {
        for (let i = 0, store; store = selected[++i];) {
          if (store.brandId !== selected[0].brandId) {
            Com.$messager(i18n.tt("请选择同一品牌下的门店"));
            return;
          }
        }
      }
      cb && cb();
      this.$nextTick(() => {
        this.$emit('callback', selected, 'selectStore');
      });
    },
    $doClose() {
      for (let attr in this.searchData) {
        this.searchData[attr] = this.defaultData[attr];
      }
      this.$nextTick(() => {
        this.$emit('close', 'selectStore');
      });
    },
    $changeRegion(data) {
      Com.cloneObjects(this.lData, data);
      this.$emit('changeRegion', data);
    },
    $resetClick() {
      const {
        brandId
      } = this;
      if (Array.isArray(brandId) && brandId.length) {
        this.lData.brandIdList = [].concat(brandId);
      } else if (!Array.isArray(brandId) && brandId !== 0) {
        this.lData.brandIdList = [brandId];
      } else {
        this.lData.brandIdList = [];
      }
      for (let attr in this.searchData) {
        this.searchData[attr] = this.defaultData[attr];
      }
      this.brandName = '';
      this.$emit('resetClick');
    },
    $doSearchByKeyWord(key, param) {
      this.$doClearList(key);
      let keyWord = param[0];
      keyWord && (this.keyWord = keyWord);
      if (keyWord.trim()) {
        if (key === 'brand') {
          this.searchBrandData = [{
            brandName: i18n.tt("搜索中，请稍候.."),
            brandId: 0
          }];
        }
        if (searchTimer) {
          clearTimeout(searchTimer);
        }
        const data = {
          pageNow: 1,
          pageSize: 9999
        };
        let url = '';
        let tip = '';
        if (key === 'brand') {
          data.brandName = keyWord;
          url = 'findBrandList';
          tip = i18n.tt("查无该品牌，请重新输入！");
        }
        searchTimer = setTimeout(() => {
          this.$service.get(url, data, {
            isHideLoading: true
          }).then(result => {
            if (!result.$.checkResult(result, true)) {
              return;
            }
            let searchData = [];
            if (key === 'brand') {
              if (result.data.rows.length) {
                searchData = result.data.rows;
              } else {
                this.searchBrandData = [{
                  brandName: tip,
                  brandId: 0
                }];
                return;
              }
              this.searchBrandData = [].concat(searchData);
            }
          });
        }, 300);
      }
    },
    $doSelectByKeyWord(key, param) {
      const item = param[0];
      let name = '';
      if (key === 'brand') {
        if (item.brandId) {
          name = item.brandName;
        } else {
          this.searchBrandData = [];
        }
        this.$set(this, 'brandName', name);
        this.lData.brandIdList = [item.brandId];
      }
    },
    $doClearList(key) {
      if (key === 'brand') {
        this.lData.brandIdList = [];
        this.searchBrandData = [];
      }
    },
    $input(value) {
      if (!this.lData.storeId) {
        this.lData.storeId = null;
      } else {
        this.lData.storeId = Number(this.lData.storeId);
      }
    },
    $modifyColumns(columns) {
      this.leftColumns = columns;
      const fieldList = columns.map(item => item.dataIndex);
      const fieldNameList = columns.map(item => item.title);
      if (!this.sid) {
        Com.vm.$service.get('addFieldSort', {
          type: 15,
          fieldList,
          fieldNameList
        }).then(result => {
          if (result.$.checkResult(result, true)) {
            this.sid = result.data.sid;
          }
        });
      } else {
        Com.vm.$service.get('updateFieldSort', {
          sid: this.sid,
          fieldList,
          fieldNameList
        }).then(result => {
          if (result.$.checkResult(result, true)) {}
        });
      }
    }
  }
}
</script>
<style scoped lang="scss" scoped="true">

.transfer-footer {
  padding: 12px 10px;
  font-size: 0;
  text-align: center;
  border-top: 1px solid #cdcdcd;
}
.mr8 {
  margin-right: 8px;
}

.screen-btn {
  margin: 2px 0 !important;
}

</style>

<style lang="scss">

.comp-sel-store {
  .cd-form--inline {
    display: flex;
    flex-wrap: wrap;
  }

  .select-org-btn {
    .box {
      height: 28px;
      line-height: 28px;

      .select-info {
        border: 1px solid #d7d7d7;
      }
    }
  }
}

</style>

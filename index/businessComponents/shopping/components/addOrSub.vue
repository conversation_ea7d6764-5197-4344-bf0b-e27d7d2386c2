<template>
  <section class="din add-or-sub">
    <section
      v-if="product.disabled"
      class="csp din box"
      style="color: #d8ac70; font-size: 12px">
      {{ product.warnText || "" }}
    </section>
    <section
      v-else-if="productLisUtil.isSingleProWithOutSpecial(product) || type == 'shoppingCartList' || pure"
      class="csp din box">
      <img
        @click="clickSub(product, index)"
        v-if="product.count > 0"
        class="sub"
        :src="product.count == 1 && !canZero ? ICON.subDisabled : ICON.sub" />
      <span
        class="count"
        v-if="product.count > 0">
        {{ product.count }}
      </span>
      <img
        @click="clickAdd(product, index)"
        class="add"
        :src="ICON.add" />
    </section>
    <section
      v-else-if="productLisUtil.isSingleProWithSpecial(product) && !pure && type == 'productList'"
      class="din btn csp"
      @click="clickAdd(product, index)">
      {{ $lang(`选规格`) }}
      <span
        v-if="product.count > 0"
        class="din tag-count">
        {{ product.count }}
      </span>
    </section>
    <section v-if="modalConfig.show">
      <modalSelect
        v-if="modalConfig.showSelect.show"
        @close="$closeModal('showSelect')"
        :product="product"
        :shoppingCartList="shoppingCartList"
        :index="index"
        :order="order"></modalSelect>
    </section>
  </section>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import { eventBus } from 'build/plugs/utils';
import modalSelect from './modal-select.vue';
import productLisUtil from './../utils/productLisUtil.js';
import specialUtil from './../utils/specialUtil.js';
export default {
  name: 'add-or-sub',
  mixins: [eventBus],
  components: {
    modalSelect
  },
  props: {
    order: {
      type: Object,
      default: () => null
    },
    type: {
      type: String,
      default: 'productList' // productList or shoppingCartList
    },
    product: {
      type: Object,
      default: () => {
        return null;
      }
    },
    shoppingCartList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    productList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    index: {
      type: Number,
      default: 0
    },
    pure: {
      // 纯净模式，不收餐品的信息影响。只控制数量
      type: Boolean,
      default: false
    },
    canZero: {
      // 允许数量为0
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      ICON: {
        sub: require('project/index/static/images/icon/icon_sub.png'),
        subDisabled: require('project/index/static/images/icon/icon_sub_disabled.png'),
        add: require('project/index/static/images/icon/icon_add.png')
      },
      productLisUtil,
      modalConfig: {
        show: false,
        showSelect: {
          show: false
        }
      }
    };
  },
  created() {
    console.log('add-or-sub.vue...');
  },
  methods: {
    checkCanAdd() {
      let pass = true,
        tips = '',
        pro = this.product;
      // 检查库存是否满足
      if (pro.hasStock && pro.stockNum - pro.count <= 0) {
        pass = false;
        tips = i18n.tt("已超出库存");
      }
      // 检查是否超出最多可选数
      else if (pro.maxNum && pro.maxNum - pro.count <= 0) {
        pass = false;
        tips = i18n.tt("已超出最大可数");
      }
      // 检查购物车情况下，特殊要求的校验
      else if (this.type === 'shoppingCartList') {
        // 先校验特殊要求库存是否足够
        let proBeCount = pro.count + 1;
        specialUtil.loopSpecialItems(pro, item => {
          if (pass === true) {
            let contextStockNum = item.stockNum - specialUtil.countItemSelectLen(item, item.count || 1, proBeCount);
            if (contextStockNum < 0) {
              pass = false;
              tips = i18n.tt("添加失败，【{0}】已超出库存", [item.name]);
            }
          }
        });
      }
      if (pass) {
        // 检查是否超出原订单餐品数量
        let orderProducts = this.order.orderProducts || [];
        let pidMap = new Map(); // Map<pid, num>, num是原订单中的餐品选择数量
        let oriPro = this.productList.find(p => p.pid == pro.pid);
        orderProducts.map(pro => {
          if (pidMap.has(pro.pid)) {
            let existNum = pidMap.get(pro.pid);
            pidMap.set(pro.pid, existNum + pro.num);
          } else {
            pidMap.set(pro.pid, pro.num);
          }
        });
        let orderProNum = pidMap.get(pro.pid);
        if (oriPro.count + 1 > orderProNum) {
          pass = false;
          tips = i18n.tt("数量不能超过原订单餐品数量");
        }
      }

      // 检查是否超出原订单-特殊要求数量
      if (pass && Com.base.afterSaleLimitInOrder) {
        let uidMaxCountInOrderMap = specialUtil.getUidMaxCountMapInOrder(this.product, this.order, this.type === 'shoppingCartList');
        let noCountFields = ['standard', 'propertys', 'choices'];
        let proBeCount = pro.count + 1;
        specialUtil.loopSpecialItems(pro, item => {
          if (pass && item.active) {
            let isOver = false;
            if (noCountFields.includes(item.field)) {
              if (proBeCount > uidMaxCountInOrderMap.get(item.uid)) {
                isOver = true;
              }
            } else {
              if (proBeCount * item.count > uidMaxCountInOrderMap.get(item.uid)) {
                isOver = true;
              }
            }
            if (isOver) {
              pass = false;
              tips = i18n.tt("添加失败，【{0}】已超出原订单数量", [item.name]);
            }
          }
        });
      }
      return {
        pass,
        tips
      };
    },
    $openModal(key) {
      this.modalConfig.show = true;
      this.modalConfig[key].show = true;
    },
    $closeModal(key) {
      this.modalConfig.show = false;
      this.modalConfig[key].show = false;
    },
    $doClose() {
      this.visible = false;
      setTimeout(() => {
        this.$emit('close', 'shopping');
      });
    },
    $doSave() {
      // let _this = this;
      //   let ref = this.$refs.refCreateAgreement
      //   if(ref && ref.save) {
      //     ref.save((agreementList) => {
      //       _this.visible = false;
      //       setTimeout(() => {
      //         _this.$emit('save', agreementList, );
      //       })

      //     })
      //   }else {
      //     _this.visible = false;
      //     setTimeout(() => {
      //       _this.$emit('save', null, );
      //     })

      //   }
    },
    clickSub() {
      if (this.product.count == 1 && !this.canZero) {
        return;
      }
      if (this.pure) {
        this.product.count--;
        return;
      }
      this.$emit('clickSub', this.product, this.index);
    },
    clickAdd() {
      let {
        pass,
        tips
      } = this.checkCanAdd();
      if (!pass) {
        Com.$messager(tips || '');
        return;
      }
      if (this.pure) {
        this.product.count++;
        return;
      }
      // 从餐品列表新加进来的场景：走弹窗的情况
      if (this.type === 'productList' && productLisUtil.isSingleProWithSpecial(this.product)) {
        this.$openModal('showSelect');
        return;
      } else {
        // 不走弹窗就回调回去自己处理
        this.$emit('clickAdd', this.product, this.index);
      }
    }
  },
  watch: {},
  computed: {}
}
</script>
<style scoped lang="scss" scoped="true">
            
.btn {
  padding: 0px 12px;
  height: 24px;
  background: #ECD2A1;
  border-radius: 29px 29px 29px 29px;
  position: relative;
  text-align: center;
  line-height: 24px;
}

.box {
  font-size: 20px;
  display: flex;
  align-items: center;
  .sub {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: #FFFFFF;
    opacity: 1;
    border: 1px solid #ECD2A1;
    border-radius: 50%;
    line-height: 19px;
    text-align: center;

  }
  .count {
    display: inline-block;
    font-weight: bold;
    color: #333333;
    margin: 0 14px;
    font-size: 14px;
  }

  .add {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: #ECD2A1;
    border-radius: 50%;
    line-height: 21px;
    text-align: center;
  }

  .disable {
    background: #E2E2E2;
    color: white;
  }

}

.tag-count {
  position: absolute;
  right: -2px;
  top: -8px;
  min-width: 18px;
  height: 18px;
  background: #ECD2A1;
  border-radius: 12px;
  line-height: 16px;
  text-align: center;
  color: white;
  font-size: 12px;
}

</style>

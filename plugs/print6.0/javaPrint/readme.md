
###### 文件用途
javaPrint文件下的内容方法用于dc 触发打印逻辑时java方法调用，此文件更新到静态服务器上，dc通过url引入

###### 文件更新
文件更新到静态服务器上，以下是更新路径
qc环境服务器路径：
  /data/candao-front-static/webapps/ROOT/javaPrintTest （更新时注意，千万不要更新错更新到正式文件去了）
正式环境服务器路径：
  /data/candao-front-static/webapps/ROOT/javaPrint （慎重更新）

文件更新到服务器后，需求用postman 请求以下接口清除缓存才生效
qc环境接口
 url：http://candao-api-gateway.ab-v6-qc.can-dao.com/WebAction
 请求参数：
  {
      "actionName": "candao.cloudPrint.reloadJsScript",
      "content": {
          "cleanScript": true,
          "cleanTemplate": true,
          "storeId": 0
      }
  }

  正式环境url：http://gateway.can-dao.com.hk/WebAction
  请求参数更qc一致

  ###### 注意注意注意
  1. 需更新到文件为不带_es后缀的文件；
  2. 由于java环境不支持es6，会有兼容问题，写逻辑时尽量尽量不要用es6，如果用了请自行转为es5；
  3. es6转es5可使用 命令行转码babel-cl 工具,基本用法如下
  4. 在终端中安装bable，输入 npm install –g babel-cli
  ```
    # 转码结果输出到标准输出
    $ babel example.js

    # 转码结果写入一个文件
    # --out-file 或 -o 参数指定输出文件
    $ babel example.js --out-file compiled.js
    # 或者
    $ babel printTemplate_es6.js -o printTemplate.js

    # 整个目录转码
    # --out-dir 或 -d 参数指定输出目录
    $ babel src --out-dir lib
    # 或者
    $ babel src -d lib
  ```
  
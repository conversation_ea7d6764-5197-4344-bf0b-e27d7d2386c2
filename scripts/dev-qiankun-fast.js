process.env.NODE_ENV = 'development';
const webpack = require('webpack');
var webpackConfig = require('./webpack.init');
const merge = require("webpack-merge");
const projectsInfo = require('./project.init');

// 微前端配置
const microAppName = projectsInfo.projectName.replace('candao-', '') // candao-ocrm -> ocrm

// 为快速开发模式优化 webpack 配置
let fastWebpackConfig = Object.assign({}, webpackConfig);

// 完全禁用 CSS 提取，使用内联样式以获得最快的热更新
fastWebpackConfig.plugins = webpackConfig.plugins.filter(plugin => 
  plugin.constructor.name !== 'ExtractTextPlugin'
);

// 添加热更新插件
fastWebpackConfig.plugins.push(new webpack.HotModuleReplacementPlugin());

// 使用更快的 source map
fastWebpackConfig.devtool = '#cheap-module-eval-source-map';

const config = merge(fastWebpackConfig, {
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  output: {
    library: microAppName,
    libraryTarget: 'umd',
    jsonpFunction: `webpackJsonp_${microAppName}`,
  },
  // 极速监听配置
  watchOptions: {
    aggregateTimeout: 100, // 最小延迟
    poll: 500, // 更频繁的轮询
    ignored: [
      /node_modules/,
      /\.git/,
      /dist/,
      /build/
    ],
  },
  // 优化解析
  resolve: {
    ...webpackConfig.resolve,
    // 减少解析步骤
    modules: ['node_modules'],
    // 缓存解析结果
    cacheWithContext: false,
  },
  // 优化模块处理
  module: {
    ...webpackConfig.module,
    // 跳过不必要的解析
    noParse: /jquery|lodash/,
  }
})

// 移除 loader 字符中的 &publicPath=/assets/
config.module.rules.map((item) => {
  if (item.loader) {
    if (typeof item.loader === 'string') {
      item.loader = item.loader.replace(/&publicPath=\/assets\//, '')
    } else if (Array.isArray(item.loader)) {
      item.loader = item.loader.map((loader) => {
        return loader.replace(/&publicPath=\/assets\//, '')
      })
    }
  }
  return item
})

console.log('[Fast Dev Mode] 启用极速开发模式，CSS 将内联处理以获得最快的热更新速度');

// 启用热更新模式
require("./run")(config, {isHotMode: true});

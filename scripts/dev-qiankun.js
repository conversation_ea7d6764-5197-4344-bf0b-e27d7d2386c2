process.env.NODE_ENV = 'development';
const webpack = require('webpack');
var webpackConfig = require('./webpack.init');
const merge = require("webpack-merge");
const projectsInfo = require('./project.init');

// 微前端配置
const microAppName = projectsInfo.projectName.replace('candao-', '') // candao-ocrm -> ocrm

// 为开发模式优化 webpack 配置
let optimizedWebpackConfig = Object.assign({}, webpackConfig);

// 开发模式下禁用 CSS 提取以提高热更新速度
optimizedWebpackConfig.plugins = webpackConfig.plugins.filter(plugin =>
  plugin.constructor.name !== 'ExtractTextPlugin'
);

// 添加热更新插件
optimizedWebpackConfig.plugins.push(new webpack.HotModuleReplacementPlugin());

const config = merge(optimizedWebpackConfig, {
  devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
    },
  },
  output: {
    library: microAppName,
    libraryTarget: 'umd',
    jsonpFunction: `webpackJsonp_${microAppName}`,
  },
  // 优化监听配置
  watchOptions: {
    aggregateTimeout: 200, // 减少延迟
    poll: 1000, // 使用更合理的轮询间隔
    ignored: /node_modules/, // 忽略 node_modules
  },
})

// 移除 loader 字符中的 &publicPath=/assets/
config.module.rules.map((item) => {
  if (item.loader) {
    if (typeof item.loader === 'string') {
      item.loader = item.loader.replace(/&publicPath=\/assets\//, '')
    } else if (Array.isArray(item.loader)) {
      item.loader = item.loader.map((loader) => {
        return loader.replace(/&publicPath=\/assets\//, '')
      })
    }
  }
  return item
})

require("./run")(config);
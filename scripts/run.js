/*
 * 引入必要插件 与初始化相关
 */
process.noDeprecation = true;

const webpack = require('webpack');
var webpackCfg = require('./webpack.init');

const path = require('path');
const fs = require('fs');

const HtmlWebpackPlugin = require('html-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');
// const WebpackMd5Hash = require('webpack-md5-hash');
// const WebpackSplitHash = require('webpack-split-hash');
// const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const UglifyJsPlugin = webpack.optimize.UglifyJsPlugin;
const proxyMiddleware = require('http-proxy-middleware'); // 引入http代理中间件 - by Allen

const FileSystem = require('../util/memoryFileSystem/fileSystem');
const memoryFsMiddleware = require('../util/memoryFileSystem/memoryFsMiddlerware');
const childProcess = require('child_process');

// var OpenBrowserPlugin = require('open-browser-webpack-plugin');

// 获取项目相关信息
const projectsInfo = require('./project.init');
if(!projectsInfo || !projectsInfo.status){
  console.error(`[ERR!] get projectsInfo fail! ${JSON.stringify(projectsInfo||{})}`);
  return ;
}

// 代理Actions配置枚举
const actionsEmuerate = [
  '/*Action',
  '/*Cache',
  '/Local',
  '/thridConfig',
  '/UserYaZuo', // 对接雅座action
  '/UserWangJie', // 对接网捷action
  '/UserXueLi', // 对接雪沥action
  '/TpAction', // 对接雪沥action
  '/UserYouZan', // 对接有赞action
  '/RemappingAction', // 对接短信
  '/UserUnify', // Azure
  '/UserMc', //冥晨
]
/*console.log(projectsInfo);
return ;
*/

/**
 * 打包方法
 * @param {*} webpackCfg webpack 配置
 * @param {*} isHotMode 是否热更新
 * @param {*} isNotListenService 不监听服务器 - by faye
 */
module.exports = function(webpackConfig, {isHotMode = false, isNotListenService = false}={}) {
  webpackConfig = webpackConfig || webpackCfg;
  //补全 alias
  webpackConfig.resolve.alias = {
    'build': path.join(__dirname, '..')
    ,'modules': path.join(__dirname, '..' , '..' ,'node_modules')

    //===
    ,'project': path.join(projectsInfo.projectPath )
    ,'static': path.join(projectsInfo.projectsPath , 'static')
    ,'@': projectsInfo.projectsPath
  }
  // console.log(webpackConfig.resolve.alias);

  // 全局 常量 与 方法
  const isRelease = process.env.NODE_ENV && process.env.NODE_ENV === 'production' //是否正式环境
  const distPath = path.join(__dirname, '..','dist'); //发布路径
  // jenson - 2020/5/8
  var libPath = '//static.can-dao.com/lib/';

  var gitPath = fs.existsSync(path.join(projectsInfo.projectPath,'.git')) ?  projectsInfo.projectPath : projectsInfo.projectsPath;

  // 获取时间戳作为版本号
  const GET_TIME = new Date().getTime();

  console.log('GET_TIME:',GET_TIME)
  // var libPath = '';
  // if(isRelease){
  //   libPath = '//static.can-dao.com/lib/';
  // }else{
  //   libPath = '//qc.can-dao.com:680/lib/';
  // }

  /*
  * 为了防止chunk文件残留历史文件
  * 所以每一次构建都需要清理掉整个目录
  */

  // 加载 shelljs 插件
  // 加上 global 是为了将所有命令变为全局命令，如下方的 exec
  require('shelljs/global');

  // 发布目录
  var disProjectTime = new Date().getTime();
  var disProjectPath = path.join(distPath,projectsInfo.projectsName,projectsInfo.projectName,projectsInfo.config.version + (isRelease?'-release':''));

  // 开始删除 输出log
  exec('rimraf '+disProjectPath+' && mkdir '+disProjectPath);
  console.log(`[OK!] Clean：${disProjectPath}`);

  /*
  * 开始根据获取到的信息配置 webpack
  */

  // 配置 webpack 的输出目录
  webpackConfig.output.path = path.join(disProjectPath,'assets');
  console.log(`[OK!] webpackConfig.output.path: ${webpackConfig.output.path}`);

  // 配置每一个 webpack-entry
  projectsInfo.entrys.forEach(ent=>{
    var entryPath = path.join(projectsInfo.projectPath,ent,'config.js');
    webpackConfig.entry[ent] = [entryPath];
    webpackConfig.plugins.push(new HtmlWebpackPlugin({
      filename:`./../${ent}.html`,
      chunks:[ent],
      template:projectsInfo.templates[ent],
      hash:true,
      inject: true,
      minify:isRelease?{
        "removeAttributeQuotes": true,
        "removeComments": true,
        "removeEmptyAttributes": true
      }:false,
      //页面替换变量
      project:projectsInfo.projectName
      ,projects:projectsInfo.projectsName
      ,pageName: projectsInfo.config.entryTitles[ent]?(projectsInfo.config.entryTitles[ent]+' - '):''
      ,projectName:projectsInfo.name || ''
      ,version:GET_TIME
      ,time:disProjectTime
      ,env:process.env.NODE_ENV||'development'
      ,min:isRelease?'.min':'' //一般只有 lib 资源用
      ,libPath:libPath //静态服务器的地址
    }));
  })


  // 动态生成版本号
  // if (fs.existsSync(`${projectsInfo.projectPath}/_version.js`)) {
  if(isRelease){
    var versionStr = `_verifyVersion&&_verifyVersion('${GET_TIME}');`;
    fs.writeFileSync(`${projectsInfo.projectPath}/_version.js`, versionStr);
    // 拷贝版本号js文件
    webpackConfig.plugins.push(
      new CopyWebpackPlugin([{
        from: `${projectsInfo.projectPath}/_version.js`,
        to:`./_version.js`,
      }])
    );
  }
  // }

  //拷贝资源库
  /*webpackConfig.plugins.push(
    new CopyWebpackPlugin([{
      from: `${dev}lib`,
      to:`./lib/`
    }
    ])
  );*/

  //压缩 与 生成 source-map
  if(isRelease){
    const CompressionWebpackPlugin = require('compression-webpack-plugin')
    webpackConfig.plugins.push(
      new UglifyJsPlugin({
        sourceMap: false
        ,compress: {
          warnings: false
        }
        ,output: {
          comments: false
        }
      })
    );
    webpackConfig.plugins.push(
      new CompressionWebpackPlugin({
        asset: '[path].gz[query]',
        algorithm: 'gzip',
        test: new RegExp(
          '\\.(' +
          ['js', 'css'].join('|') +
          ')$'
        ),
        threshold: 10240,
        minRatio: 0.8
      })
    )
    // webpackConfig.plugins.push(new WebpackMd5Hash());
    // webpackConfig.plugins.push(new WebpackSplitHash());
    // webpackConfig.devtool = '#source-map';
  }else{
    webpackConfig.devtool = '#cheap-module-source-map';
    if (isHotMode) {
      Object.keys(webpackConfig.entry).forEach(function (name) { // 打包客户端热更新代码 by allen
        webpackConfig.entry[name] = [path.join(__dirname,'./dev-client')].concat(webpackConfig.entry[name])
      })
    }
    //断点调试有时会无法调适，碰到此问题的话建议关闭生成 source-map
    //webpackConfig.devtool = '#cheap-module-eval-source-map';
  }

  // 打开窗口
  // webpackConfig.plugins.push(
  //   new OpenBrowserPlugin({ url: `http://127.0.0.1:${projectsInfo.config.port?projectsInfo.config.port:80}` })
  // )

  //开始执行 webpack
  var timer;
  var consoleBuilding = function(){
    console.log('[building,please wait..]');
    timer = setTimeout(consoleBuilding,1500);
  }
  //consoleBuilding();


  console.log('\n\n');

  var ora = require('ora');
  const spinner = ora('Building..');

  const httpProxyConfig = (app) => {  // http代理配置 - by Allen
    let proxyPath = projectsInfo.config.devUrl || 'http://qc.can-dao.com:128' // qc环境地址
    console.log('代理地址：', proxyPath)
    let proxyOption = {target: proxyPath, changeOrigin: true}
    for (let i = 0; i < actionsEmuerate.length; i++) {
      const action = actionsEmuerate[i];
      app.use(action, proxyMiddleware(proxyOption));
    }
    // app.use('/*Action', proxyMiddleware(proxyOption));
    // app.use('/*Cache', proxyMiddleware(proxyOption));
    // app.use('/Local', proxyMiddleware(proxyOption));
    // app.use('/thridConfig', proxyMiddleware(proxyOption));
    // app.use('/UserYaZuo', proxyMiddleware(proxyOption)); // 对接雅座action
    // app.use('/UserWangJie', proxyMiddleware(proxyOption)); // 对接网捷action
    // app.use('/UserXueLi', proxyMiddleware(proxyOption)); // 对接雪沥action
    // app.use('/TpAction', proxyMiddleware(proxyOption)); // 对接雪沥action
    // app.use('/UserYouZan', proxyMiddleware(proxyOption)); // 对接有赞action
  }

  const initHotMiddleWare = (compiler) => {
    const wbHotMiddleware = require('webpack-hot-middleware')(compiler, {
      heartbeat: 1000, // 减少心跳间隔以提高响应速度
      log: false, // 关闭日志以减少干扰
      reload: true, // 当 HMR 失败时自动刷新页面
      noInfo: false // 显示更新信息
    })

    // 强制刷新当 HTML 模板变化时
    compiler.hooks.compilation.tap('HotReload', (compilation) => {
      compilation.hooks.htmlWebpackPluginAfterEmit.tapAsync('HotReload', (data, cb) => {
        wbHotMiddleware.publish({ action: 'reload' })
        cb()
      })
    })

    return wbHotMiddleware;
  }

  // 监听端口
  // 用于开发环境中启用node服务器
  //if(!process.env.NODE_ENV || process.env.NODE_ENV !== 'production'){
  const runService = (compiler)=>{
    var http = require('http');
    var express = require('express');
    var ecstatic = require('ecstatic');

    var listenPort = projectsInfo.config.port?projectsInfo.config.port:80;
    var app = express();

    // 微前端开发环境允许跨域
    if (compiler.options.output.library) {
      var cors = require('cors')
      app.use(cors())
    }

    var compression = require('compression') // gzip
    // 启用gzip
    app.use(compression());

    // 本地测试灰度环境时，当请求静态文件，链接中带 /new/ 时（如/new/index.html），将请求改为不带 /new/
    app.use('/', function(req, res, next) {
      let isAxiosReq = false;
      for (let i = 0; i < actionsEmuerate.length; i++) {
        const action = actionsEmuerate[i];
        if (req.url.indexOf(action) > -1) {
          isAxiosReq = true
        }
      }
      if (req.url.indexOf('/Action') > -1) {
        isAxiosReq = true
      }
      if (req.url.indexOf('/Cache') > -1) {
        isAxiosReq = true
      }

      if (!isAxiosReq && req.url.indexOf('/new/') == 0 ) {
        req.url = req.url.split('/new')[1];
      }

      next();
    })

    if (isHotMode) { // 判断是否热更新环境
        app.use(memoryFsMiddleware(compiler, compiler.outputFileSystem,  {
          publicPath: '/', //  || webpackConfig.output.publicPath
          quiet: true
        }))
        app.use(initHotMiddleWare(compiler));
    } else {
      app.use(ecstatic({ root: disProjectPath}));
    }

    if(projectsInfo.config.useDevProxy){
      httpProxyConfig(app); // 使用代理 - by Allen
    }

    http.createServer(app).listen(listenPort);

    var os=require('os'),
      iptable={},
      ifaces=os.networkInterfaces();

    console.log('\n');
    for (var ipt in ifaces) {
      ifaces[ipt].forEach(function(details,alias){
        if (details.family=='IPv4') {
          iptable[ipt+(alias?':'+alias:'')]=details.address;
          console.log("> [server start] http://"+details.address+(listenPort==80?"":(":"+listenPort)));
          //spinner.info( ">>> [watching & server start] http://"+details.address+(listenPort==80?"":(":"+listenPort)) );
        }
      });
    }
    console.log('\n');
  }

  // 启动build提示
  spinner.start();
  spinner.color = 'yellow';
  //}


  const compiler = webpack(webpackConfig);


  if (isRelease) {
    // 发布环境
    compiler.run((err, stats) => {
      if (err) {
        console.log(err);
        return false;
      }
      console.log('\n\n> [builded!]' + new Date() + '\n');
      spinner.color = 'green';
      spinner.text = '[build end...]';

      // 不启动服务器，构建结束 - by faye
      if(isNotListenService) {
        spinner.succeed();
        return false;
      } else {
        if (!global.serverStart) {
          global.serverStart = true;
          runService(compiler);
        }
      }

    })
  } else if(isNotListenService) { // 本地环境不启动服务器，测试用，构建结束 - by faye
    compiler.run((err, stats) => {
      if (err) {
        console.log(err);
        return false;
      }
      console.log('\n\n> [builded!]' + new Date() + '\n');
      spinner.color = 'green';
      spinner.text = '[build end...]';

      spinner.succeed();
      return false;
    })

  } else {
    // 本地环境
    if (isHotMode) { // 判断是否热更新环境
      compiler.outputFileSystem = new FileSystem(); // 设置为内存数据库
    }
    const watchOptions = webpackConfig.watchOptions || {};
    global.serverStart = false;
    compiler.watch(watchOptions, (err, stats) => {
      if (err) {
        console.log(err);
        return false;
      }
      //console.log('[builded!]' + new Date());
      //spinner.fail('[builded!]' + new Date())
      //timer && clearTimeout(timer);


      console.log('\n\n> [builded!]' + new Date() + '\n');


      spinner.color = 'green';
      spinner.text = '[watching..]';

      if (!global.serverStart) {
        global.serverStart = true;
        runService(compiler);
      }

    });
  }
}

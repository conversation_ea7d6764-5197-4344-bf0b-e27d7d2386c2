// const path = require('path');
// const webpack = require('webpack');
const ExtractTextPlugin = require("extract-text-webpack-plugin");
var normalCssExtract = new ExtractTextPlugin({filename:"css/[name].css",allChunks: true});
// var skinCssExtract = new ExtractTextPlugin({filename:"css/skin_[contenthash:8].css",allChunks: false,disable:false,ignoreOrder:false});
const isPro = (process.env.NODE_ENV && process.env.NODE_ENV === 'production');
const WebpackMd5Hash = require('webpack-md5-hash');
const WebpackSplitHash = require('webpack-split-hash');
const XhrEvalChunkPlugin = require('xhr-eval-chunk-webpack-plugin').default;
module.exports = {
  entry: {},
  output: {
    path: '',
    filename: `[name]-[hash:8].js`,
    chunkFilename: `chunk/[name]-[hash:8].js`,
    publicPath:'./assets/',
  },
  watch:!isPro,
  watchOptions: {
    aggregateTimeout: 200, // 减少延迟时间
    poll: 1000, // 使用固定间隔而不是持续轮询
    ignored: /node_modules/, // 忽略 node_modules 目录
  },
  resolve: {
    extensions: ['.js', '.vue', '.json','.scss']
    ,alias: {

    }
  },
  module: {

    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader',
        options: {
          // 开发模式下不提取 CSS，使用 style-loader 以支持热更新
          extractCSS: isPro,
          loaders: {
            css: isPro ? normalCssExtract.extract({
              use: ['css-loader?minimize!sass-loader'],
              fallback: 'vue-style-loader'
            }) : 'vue-style-loader!css-loader!sass-loader'
            ,sass: isPro ? normalCssExtract.extract({
              use: ['css-loader?minimize!sass-loader'],
              fallback: 'vue-style-loader'
            }) : 'vue-style-loader!css-loader!sass-loader'
            ,scss: isPro ? normalCssExtract.extract({
              use: ['css-loader?minimize!sass-loader'],
              fallback: 'vue-style-loader'
            }) : 'vue-style-loader!css-loader!sass-loader'
            ,skin: 'style-loader!css-loader'+(isPro?"?minimize":"")+'!sass-loader'
            ,postcss: [require('postcss-cssnext')()]
            ,cssModules: {
              localIdentName: `[path][name]---[local]---[${isPro ? 'contenthash' : 'hash'}:base64:5]`,
              camelCase: true
            }
          }
      	  ,exclude: /node_modules/
          ,query: {
            presets: ['es2015','stage-0']
          }
        }

      }

      ,{
        test: /\.js$/
        ,exclude: /node_modules/
        ,use:{
          loader: 'babel-loader'
          ,options:{
            plugins: ['transform-runtime']
            ,presets: ['es2015','stage-0']
            ,compact: false
          }
        }

      }

      ,{
        test: /\.css$/,
        use: isPro ? ExtractTextPlugin.extract({
          fallback: 'style-loader',
          use: 'css-loader?minimize!postcss-loader!sass-loader'
        }) : ['style-loader', 'css-loader', 'postcss-loader', 'sass-loader']
      }
      ,{
        test: /\.(scss|sass)$/,
        use: isPro ? ExtractTextPlugin.extract({
          fallback: 'style-loader',
          use: 'css-loader?minimize!postcss-loader!sass-loader'
        }) : ['style-loader', 'css-loader', 'postcss-loader', 'sass-loader']
      }
      ,{ test: /\.(eot|woff|ttf|svg|mp3|ogg|wav)$/, loader: `file-loader?name=fonts/[name]-[${isPro ? 'contenthash' : 'hash'}].[ext]&publicPath=/assets/` }
      ,{ test: /\.(xls|xlsx|xlsm|doc|docx|pdf)$/, loader: "file-loader?name=/extTool/[name].[ext]" }
      ,{ test: /\.(md|txt)$/, loader: "html-loader" }
      ,{
        test: /\.(png|jpe?g|gif|svg)$/
        ,loader: [
          // 'url-loader?limit=8000&name=images/[hash:8].[name].[ext]'
          `url-loader?limit=8000&name=images/[${isPro ? 'contenthash' : 'hash'}:8].[name].[ext]&publicPath=/assets/`
          //,'image-webpack-loader?{optimizationLevel: 7, interlaced: false, pngquant:{quality: "75-85", speed: 4}, mozjpeg: {quality: 80}}'
        ]
        ,exclude: /node_modules/
      }

    ]
  },

  plugins: [

    normalCssExtract
    ,new WebpackMd5Hash()
   ,new WebpackSplitHash()
   ,new XhrEvalChunkPlugin()
    ,new WebpackSplitHash()
  ]

  ,devtool: ""
}

